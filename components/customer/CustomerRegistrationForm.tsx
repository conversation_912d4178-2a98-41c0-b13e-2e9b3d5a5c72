"use client";

import React, { useState } from "react";
import {
  X,
  User,
  Mail,
  Phone,
  Calendar,
  Award,
  MessageSquare,
  Check,
} from "lucide-react";
import { createCustomer } from "@/lib/customer";
import {
  CreateCustomerRequest,
  CustomerFormData,
  CUSTOMER_SEGMENTS,
  CONTACT_METHODS,
} from "@/types/customer";

interface CustomerRegistrationFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

const CustomerRegistrationForm: React.FC<CustomerRegistrationFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const [formData, setFormData] = useState<CustomerFormData>({
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    membership_number: "",
    customer_segment: "regular",
    preferred_contact_method: "email",
    notes: "",
    date_of_birth: "",
    marketing_consent: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<CustomerFormData>>({});

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({
        ...prev,
        [name]: checked,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }

    // Clear error when user starts typing
    if (errors[name as keyof CustomerFormData]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Partial<CustomerFormData> = {};

    // Required fields
    if (!formData.first_name.trim()) {
      newErrors.first_name = "First name is required";
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = "Last name is required";
    }

    // Email validation (optional but must be valid if provided)
    if (formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email.trim())) {
        newErrors.email = "Please enter a valid email address";
      }
    }

    // Phone validation (optional but must be valid if provided)
    if (formData.phone_number.trim()) {
      const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
      if (!phoneRegex.test(formData.phone_number.trim())) {
        newErrors.phone_number = "Please enter a valid phone number";
      }
    }

    // Date of birth validation (optional but must be valid if provided)
    if (formData.date_of_birth) {
      const birthDate = new Date(formData.date_of_birth);
      const today = new Date();
      if (birthDate > today) {
        newErrors.date_of_birth = "Date of birth cannot be in the future";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare the request data
      const requestData: CreateCustomerRequest = {
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
      };

      // Add optional fields only if they have values
      if (formData.email.trim()) {
        requestData.email = formData.email.trim();
      }

      if (formData.phone_number.trim()) {
        requestData.phone = formData.phone_number.trim();
      }

      if (formData.membership_number.trim()) {
        requestData.membership_number = formData.membership_number.trim();
      }

      if (formData.customer_segment) {
        requestData.customer_segment = formData.customer_segment;
      }

      if (formData.preferred_contact_method) {
        requestData.preferred_contact_method =
          formData.preferred_contact_method;
      }

      if (formData.notes.trim()) {
        requestData.notes = formData.notes.trim();
      }

      if (formData.date_of_birth) {
        requestData.date_of_birth = formData.date_of_birth;
      }

      requestData.marketing_consent = formData.marketing_consent;

      // Create the customer
      await createCustomer(requestData);
      onSuccess();
    } catch (error) {
      console.error("Error creating customer:", error);
      alert(
        error instanceof Error
          ? error.message
          : "Failed to create customer. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200">
          <div className="flex items-center">
            <User className="h-6 w-6 text-primary mr-2" />
            <h3 className="text-lg font-medium text-gray-900">
              Add New Customer
            </h3>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="mt-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">
              Basic Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* First Name */}
              <div>
                <label
                  htmlFor="first_name"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  First Name *
                </label>
                <input
                  id="first_name"
                  name="first_name"
                  type="text"
                  required
                  value={formData.first_name}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.first_name ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="Enter first name"
                />
                {errors.first_name && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.first_name}
                  </p>
                )}
              </div>

              {/* Last Name */}
              <div>
                <label
                  htmlFor="last_name"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Last Name *
                </label>
                <input
                  id="last_name"
                  name="last_name"
                  type="text"
                  required
                  value={formData.last_name}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.last_name ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="Enter last name"
                />
                {errors.last_name && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.last_name}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">
              Contact Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Email */}
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  <Mail className="inline h-4 w-4 mr-1" />
                  Email Address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.email ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="Enter email address"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              {/* Phone Number */}
              <div>
                <label
                  htmlFor="phone_number"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  <Phone className="inline h-4 w-4 mr-1" />
                  Phone Number
                </label>
                <input
                  id="phone_number"
                  name="phone_number"
                  type="tel"
                  value={formData.phone_number}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.phone_number ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="Enter phone number"
                />
                {errors.phone_number && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.phone_number}
                  </p>
                )}
              </div>
            </div>

            {/* Preferred Contact Method */}
            <div className="mt-4">
              <label
                htmlFor="preferred_contact_method"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Preferred Contact Method
              </label>
              <select
                id="preferred_contact_method"
                name="preferred_contact_method"
                value={formData.preferred_contact_method}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                {CONTACT_METHODS.map((method) => (
                  <option key={method.value} value={method.value}>
                    {method.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Customer Details */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">
              Customer Details
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Membership Number */}
              <div>
                <label
                  htmlFor="membership_number"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Membership Number
                </label>
                <input
                  id="membership_number"
                  name="membership_number"
                  type="text"
                  value={formData.membership_number}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Enter membership number"
                />
              </div>

              {/* Customer Segment */}
              <div>
                <label
                  htmlFor="customer_segment"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  <Award className="inline h-4 w-4 mr-1" />
                  Customer Segment
                </label>
                <select
                  id="customer_segment"
                  name="customer_segment"
                  value={formData.customer_segment}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  {CUSTOMER_SEGMENTS.map((segment) => (
                    <option key={segment.value} value={segment.value}>
                      {segment.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Date of Birth */}
            <div className="mt-4">
              <label
                htmlFor="date_of_birth"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                <Calendar className="inline h-4 w-4 mr-1" />
                Date of Birth
              </label>
              <input
                id="date_of_birth"
                name="date_of_birth"
                type="date"
                value={formData.date_of_birth}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.date_of_birth ? "border-red-300" : "border-gray-300"
                }`}
              />
              {errors.date_of_birth && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.date_of_birth}
                </p>
              )}
            </div>

            {/* Notes */}
            <div className="mt-4">
              <label
                htmlFor="notes"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                <MessageSquare className="inline h-4 w-4 mr-1" />
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                rows={3}
                value={formData.notes}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Enter any additional notes about the customer"
              />
            </div>
          </div>

          {/* Marketing Consent */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">
              Preferences
            </h4>
            <div className="flex items-center">
              <input
                id="marketing_consent"
                name="marketing_consent"
                type="checkbox"
                checked={formData.marketing_consent}
                onChange={handleChange}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
              <label
                htmlFor="marketing_consent"
                className="ml-2 block text-sm text-gray-900"
              >
                <Check className="inline h-4 w-4 mr-1" />I consent to receive
                marketing communications
              </label>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Customer agrees to receive promotional emails, SMS, and other
              marketing materials.
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating Customer...
                </>
              ) : (
                "Create Customer"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerRegistrationForm;
