"use client";

import React from 'react';
import {
  X,
  User,
  Calendar,
  Clock,
  MapPin,
  Smartphone,
  Award,
  Gift,
  FileText,
  Mail,
  Phone,
  CreditCard,
  Activity,
} from 'lucide-react';
import {
  CustomerVisit,
  getVisitTypeLabel,
  getVisitSourceLabel,
  getVisitTypeColor,
  getVisitSourceColor,
  formatVisitTime,
  formatVisitDate,
  formatVisitDateTime,
  getDurationDisplay,
  isRecentVisit,
} from '@/types/customer-visit';
import {
  formatCustomerName,
  getCustomerSegmentLabel,
  getCustomerStatusLabel,
  getCustomerSegmentColor,
  getCustomerStatusColor,
} from '@/types/customer';

interface CustomerVisitDetailsModalProps {
  visit: CustomerVisit;
  onClose: () => void;
}

const CustomerVisitDetailsModal: React.FC<CustomerVisitDetailsModalProps> = ({
  visit,
  onClose,
}) => {
  const customer = visit.customer;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0 h-12 w-12">
              <div className="h-12 w-12 rounded-full bg-primary-light flex items-center justify-center">
                <span className="text-lg font-medium text-primary">
                  {formatCustomerName(customer).charAt(0)}
                </span>
              </div>
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Visit Details
              </h2>
              <p className="text-sm text-gray-500">
                {formatCustomerName(customer)} • {formatVisitDateTime(visit.visit_time)}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Visit Information */}
            <div className="lg:col-span-2 space-y-6">
              {/* Visit Details */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Visit Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Visit ID</label>
                    <p className="mt-1 text-sm text-gray-900 font-mono">{visit.visit_id}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Visit Date</label>
                    <p className="mt-1 text-sm text-gray-900 flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                      {formatVisitDate(visit.visit_date)}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Visit Time</label>
                    <p className="mt-1 text-sm text-gray-900 flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-gray-400" />
                      {formatVisitTime(visit.visit_time)}
                      {isRecentVisit(visit.visit_time) && (
                        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Recent
                        </span>
                      )}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Duration</label>
                    <p className="mt-1 text-sm text-gray-900 flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-gray-400" />
                      {getDurationDisplay(visit.duration_minutes)}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Visit Type</label>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getVisitTypeColor(visit.visit_type)}`}>
                        {getVisitTypeLabel(visit.visit_type)}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Source</label>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getVisitSourceColor(visit.source)}`}>
                        {getVisitSourceLabel(visit.source)}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Device Serial</label>
                    <p className="mt-1 text-sm text-gray-900 flex items-center">
                      <Smartphone className="h-4 w-4 mr-2 text-gray-400" />
                      {visit.device_serial_num}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Source Record ID</label>
                    <p className="mt-1 text-sm text-gray-900 font-mono">{visit.source_record_id}</p>
                  </div>
                </div>
              </div>

              {/* Loyalty & Rewards */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Award className="h-5 w-5 mr-2" />
                  Loyalty & Rewards
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Loyalty Visit</label>
                    <div className="mt-1">
                      {visit.is_loyalty_visit ? (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          <Award className="h-3 w-3 mr-1" />
                          Yes
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          No
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Points Earned</label>
                    <p className="mt-1 text-sm text-gray-900 flex items-center">
                      <Award className="h-4 w-4 mr-2 text-gray-400" />
                      {visit.loyalty_points_earned} points
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Reward Redeemed</label>
                    <div className="mt-1">
                      {visit.reward_redeemed ? (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          <Gift className="h-3 w-3 mr-1" />
                          Yes
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          No
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {visit.redemption_id && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Redemption ID</label>
                      <p className="mt-1 text-sm text-gray-900 font-mono">{visit.redemption_id}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Notes */}
              {visit.notes && (
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Notes
                  </h3>
                  <p className="text-sm text-gray-700 whitespace-pre-wrap">{visit.notes}</p>
                </div>
              )}

              {/* Timestamps */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Timestamps
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Created At</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(visit.created_at).toLocaleString()}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Updated At</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(visit.updated_at).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Customer Information Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Customer Information
                </h3>
                
                <div className="space-y-4">
                  {/* Customer Avatar and Name */}
                  <div className="text-center">
                    <div className="mx-auto h-16 w-16 rounded-full bg-primary-light flex items-center justify-center mb-3">
                      <span className="text-xl font-medium text-primary">
                        {formatCustomerName(customer).charAt(0)}
                      </span>
                    </div>
                    <h4 className="text-lg font-medium text-gray-900">
                      {formatCustomerName(customer)}
                    </h4>
                    <div className="flex justify-center space-x-2 mt-2">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getCustomerSegmentColor(customer.customer_segment)}`}>
                        {getCustomerSegmentLabel(customer.customer_segment)}
                      </span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getCustomerStatusColor(customer.status)}`}>
                        {getCustomerStatusLabel(customer.status)}
                      </span>
                    </div>
                  </div>

                  {/* Customer Details */}
                  <div className="space-y-3">
                    {customer.membership_number && (
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Membership #</label>
                        <p className="mt-1 text-sm text-gray-900 flex items-center">
                          <CreditCard className="h-4 w-4 mr-2 text-gray-400" />
                          {customer.membership_number}
                        </p>
                      </div>
                    )}
                    
                    {customer.email && (
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Email</label>
                        <p className="mt-1 text-sm text-gray-900 flex items-center">
                          <Mail className="h-4 w-4 mr-2 text-gray-400" />
                          {customer.email}
                        </p>
                      </div>
                    )}
                    
                    {customer.phone_number && (
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Phone</label>
                        <p className="mt-1 text-sm text-gray-900 flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-400" />
                          {customer.phone_number}
                        </p>
                      </div>
                    )}
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Registration Date</label>
                      <p className="mt-1 text-sm text-gray-900 flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                        {new Date(customer.registration_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="pt-4 border-t border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h5>
                    <div className="space-y-2">
                      {customer.email && (
                        <a
                          href={`mailto:${customer.email}`}
                          className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                        >
                          <Mail className="h-4 w-4 mr-2" />
                          Send Email
                        </a>
                      )}
                      {customer.phone_number && (
                        <a
                          href={`tel:${customer.phone_number}`}
                          className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                        >
                          <Phone className="h-4 w-4 mr-2" />
                          Call Customer
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomerVisitDetailsModal;
