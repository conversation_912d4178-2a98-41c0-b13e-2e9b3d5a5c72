"use client";

import React, { useState, useEffect } from "react";
import { X, Save, MapPin, AlertCircle, Package, FileText } from "lucide-react";
import {
  createStorageLocation,
  updateStorageLocation,
} from "@/lib/storage-location";
import {
  StorageLocation,
  CreateStorageLocationRequest,
  UpdateStorageLocationRequest,
  StorageLocationFormData,
  validateStorageLocationForm,
  getDefaultStorageLocationFormData,
} from "@/types/storage-location";

interface StorageLocationFormModalProps {
  storageLocation?: StorageLocation; // If provided, this is edit mode
  onSuccess: () => void;
  onCancel: () => void;
}

const StorageLocationFormModal: React.FC<StorageLocationFormModalProps> = ({
  storageLocation,
  onSuccess,
  onCancel,
}) => {
  const isEditMode = !!storageLocation;
  const [formData, setFormData] = useState<StorageLocationFormData>(
    getDefaultStorageLocationFormData()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = !!storageLocation;

  // Initialize form data for edit mode
  useEffect(() => {
    if (storageLocation) {
      setFormData({
        location_number: storageLocation.location_number || "",
        notes: storageLocation.notes || "",
      });
    }
  }, [storageLocation]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateStorageLocationForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      if (isEditMode && storageLocation) {
        // Update existing storage location
        const updateData: UpdateStorageLocationRequest = {
          notes: formData.notes.trim() || undefined,
        };
        await updateStorageLocation(storageLocation.location_id, updateData);
      } else {
        // Create new storage location
        const createData: CreateStorageLocationRequest = {
          location_number: formData.location_number.trim(),
          notes: formData.notes.trim() || undefined,
        };
        await createStorageLocation(createData);
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving storage location:", error);
      setErrors({
        submit: "Failed to save storage location. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <MapPin className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditMode
                  ? "Edit Storage Location"
                  : "Create New Storage Location"}
              </h2>
              <p className="text-sm text-gray-600">
                {isEditMode
                  ? "Update storage location information"
                  : "Add a new storage location for managing items"}
              </p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Submit Error */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Error</h3>
                  <p className="text-red-700 text-sm mt-1">{errors.submit}</p>
                </div>
              </div>
            </div>
          )}

          {/* Location Number */}
          <div>
            <label
              htmlFor="location_number"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Location Number *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                id="location_number"
                name="location_number"
                type="text"
                required
                value={formData.location_number}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.location_number ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Enter location number"
                disabled={isSubmitting}
              />
            </div>
            {errors.location_number && (
              <p className="mt-1 text-sm text-red-600">
                {errors.location_number}
              </p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              A unique number to identify this storage location
            </p>
          </div>

          {/* Notes */}
          <div>
            <label
              htmlFor="notes"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Notes
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                id="notes"
                name="notes"
                rows={4}
                value={formData.notes}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.notes ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Enter notes (optional)"
                disabled={isSubmitting}
              />
            </div>
            {errors.notes && (
              <p className="mt-1 text-sm text-red-600">{errors.notes}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Additional details about this storage location (max 500
              characters)
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditMode ? "Updating..." : "Creating..."}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditMode ? "Update Location" : "Create Location"}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StorageLocationFormModal;
