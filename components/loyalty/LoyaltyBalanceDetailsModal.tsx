'use client';

import React from 'react';
import { X, Award, Calendar, Clock, User, Tag, AlertTriangle } from 'lucide-react';
import {
  LoyaltyBalance,
  formatRewardValue,
  getLoyaltyStatusColor,
  REWARD_TYPE_LABELS,
  isBalanceExpiringSoon
} from '@/types/loyalty';

interface LoyaltyBalanceDetailsModalProps {
  balance: LoyaltyBalance;
  onClose: () => void;
}

const LoyaltyBalanceDetailsModal: React.FC<LoyaltyBalanceDetailsModalProps> = ({
  balance,
  onClose
}) => {
  const isExpiringSoon = isBalanceExpiringSoon(balance.expiry_date);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <Award className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Loyalty Balance Details</h2>
              <p className="text-sm text-gray-600">Balance ID: {balance.balance_id}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Expiry Warning */}
          {isExpiringSoon && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mr-3" />
                <div>
                  <h3 className="text-yellow-800 font-medium">Expiring Soon</h3>
                  <p className="text-yellow-700 text-sm mt-1">
                    This balance will expire on {balance.expiry_date ? new Date(balance.expiry_date).toLocaleDateString() : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Basic Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <User className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Customer ID</p>
                  <p className="text-sm text-gray-900">{balance.customer_id}</p>
                </div>
              </div>

              <div className="flex items-center">
                <Tag className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Rule ID</p>
                  <p className="text-sm text-gray-900">{balance.rule_id}</p>
                </div>
              </div>

              <div className="flex items-center">
                <Award className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Reward Type</p>
                  <p className="text-sm text-gray-900">
                    {REWARD_TYPE_LABELS[balance.reward_type as keyof typeof REWARD_TYPE_LABELS] || balance.reward_type}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="w-5 h-5 mr-3 flex items-center justify-center">
                  <div className="w-3 h-3 bg-primary rounded-full"></div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Balance</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatRewardValue(balance.reward_type, balance.balance)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Status Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Status Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Current Status</p>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getLoyaltyStatusColor(balance.status)}`}>
                  {balance.status.charAt(0).toUpperCase() + balance.status.slice(1)}
                </span>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Status Description</p>
                <p className="text-sm text-gray-600">
                  {balance.status === 'active' && 'This balance is available for redemption'}
                  {balance.status === 'expired' && 'This balance has expired and cannot be redeemed'}
                  {balance.status === 'redeemed' && 'This balance has been fully redeemed'}
                </p>
              </div>
            </div>
          </div>

          {/* Date Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Date Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Earned Date</p>
                  <p className="text-sm text-gray-900">
                    {new Date(balance.earned_date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                  <p className="text-xs text-gray-500">
                    {new Date(balance.earned_date).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <Clock className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Expiry Date</p>
                  {balance.expiry_date ? (
                    <>
                      <p className="text-sm text-gray-900">
                        {new Date(balance.expiry_date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(balance.expiry_date).toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </>
                  ) : (
                    <p className="text-sm text-gray-500">No expiry date</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* System Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">System Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-700">Created At</p>
                <p className="text-sm text-gray-900">
                  {new Date(balance.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700">Last Updated</p>
                <p className="text-sm text-gray-900">
                  {new Date(balance.updated_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>
          </div>

          {/* Reward Information */}
          <div className="bg-gradient-to-r from-primary-light to-secondary-light rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Reward Information</h3>
            <div className="bg-white rounded-lg p-4">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-light rounded-full mb-4">
                  <Award className="w-8 h-8 text-primary" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-2">
                  {formatRewardValue(balance.reward_type, balance.balance)}
                </h4>
                <p className="text-sm text-gray-600">
                  {REWARD_TYPE_LABELS[balance.reward_type as keyof typeof REWARD_TYPE_LABELS] || balance.reward_type}
                </p>
                {balance.status === 'active' && (
                  <p className="text-sm text-green-600 mt-2 font-medium">
                    ✓ Available for redemption
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoyaltyBalanceDetailsModal;
