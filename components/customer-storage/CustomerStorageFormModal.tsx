"use client";

import React, { useState, useEffect } from "react";
import {
  X,
  Save,
  Package,
  AlertCircle,
  User,
  MapPin,
  Calendar,
  FileText,
} from "lucide-react";
import { createCustomerStorage, updateCustomerStorage } from "@/lib/customer-storage";
import {
  CustomerStorage,
  CreateCustomerStorageRequest,
  UpdateCustomerStorageRequest,
  CustomerStorageFormData,
  validateCustomerStorageForm,
  getDefaultCustomerStorageFormData,
} from "@/types/customer-storage";
import { Customer } from "@/types/customer";
import { StorageLocation } from "@/types/storage-location";

interface CustomerStorageFormModalProps {
  customerStorage?: CustomerStorage; // If provided, this is edit mode
  customers: Customer[];
  storageLocations: StorageLocation[];
  onSuccess: () => void;
  onCancel: () => void;
}

const CustomerStorageFormModal: React.FC<CustomerStorageFormModalProps> = ({
  customerStorage,
  customers,
  storageLocations,
  onSuccess,
  onCancel,
}) => {
  const isEditMode = !!customerStorage;
  const [formData, setFormData] = useState<CustomerStorageFormData>(
    getDefaultCustomerStorageFormData()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data for edit mode
  useEffect(() => {
    if (customerStorage) {
      setFormData({
        customer_id: customerStorage.customer_id,
        location_id: customerStorage.location_id,
        item_description: customerStorage.item_description,
        storage_date: customerStorage.storage_date.split('T')[0], // Convert to YYYY-MM-DD format
        notes: customerStorage.notes || '',
      });
    }
  }, [customerStorage]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateCustomerStorageForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      if (isEditMode && customerStorage) {
        // Update existing customer storage
        const updateData: UpdateCustomerStorageRequest = {
          location_id: formData.location_id,
          item_description: formData.item_description.trim(),
          storage_date: formData.storage_date,
          notes: formData.notes.trim() || undefined,
        };
        await updateCustomerStorage(customerStorage.storage_id, updateData);
      } else {
        // Create new customer storage
        const createData: CreateCustomerStorageRequest = {
          customer_id: formData.customer_id,
          location_id: formData.location_id,
          item_description: formData.item_description.trim(),
          storage_date: formData.storage_date,
          notes: formData.notes.trim() || undefined,
        };
        await createCustomerStorage(createData);
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving customer storage:", error);
      setErrors({
        submit: "Failed to save customer storage record. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <Package className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditMode ? "Edit Storage Record" : "Store Customer Item"}
              </h2>
              <p className="text-sm text-gray-600">
                {isEditMode
                  ? "Update customer storage information"
                  : "Create a new storage record for customer belongings"}
              </p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Submit Error */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Error</h3>
                  <p className="text-red-700 text-sm mt-1">{errors.submit}</p>
                </div>
              </div>
            </div>
          )}

          {/* Customer Selection (only for create mode) */}
          {!isEditMode && (
            <div>
              <label
                htmlFor="customer_id"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Customer *
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <select
                  id="customer_id"
                  name="customer_id"
                  required
                  value={formData.customer_id}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.customer_id ? "border-red-300" : "border-gray-300"
                  }`}
                  disabled={isSubmitting}
                >
                  <option value="">Select a customer</option>
                  {customers.map((customer) => (
                    <option key={customer.customer_id} value={customer.customer_id}>
                      {customer.first_name} {customer.last_name} - {customer.email}
                    </option>
                  ))}
                </select>
              </div>
              {errors.customer_id && (
                <p className="mt-1 text-sm text-red-600">{errors.customer_id}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Select the customer who owns the item being stored
              </p>
            </div>
          )}

          {/* Storage Location */}
          <div>
            <label
              htmlFor="location_id"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Storage Location *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                id="location_id"
                name="location_id"
                required
                value={formData.location_id}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.location_id ? "border-red-300" : "border-gray-300"
                }`}
                disabled={isSubmitting}
              >
                <option value="">Select a storage location</option>
                {storageLocations.map((location) => (
                  <option key={location.location_id} value={location.location_id}>
                    {location.location_name} ({location.location_type}) - 
                    {location.capacity - location.current_usage} available
                  </option>
                ))}
              </select>
            </div>
            {errors.location_id && (
              <p className="mt-1 text-sm text-red-600">{errors.location_id}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Choose where the item will be stored
            </p>
          </div>

          {/* Item Description */}
          <div>
            <label
              htmlFor="item_description"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Item Description *
            </label>
            <div className="relative">
              <Package className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                id="item_description"
                name="item_description"
                rows={3}
                required
                value={formData.item_description}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.item_description ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Describe the item being stored"
                disabled={isSubmitting}
              />
            </div>
            {errors.item_description && (
              <p className="mt-1 text-sm text-red-600">{errors.item_description}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Provide a detailed description of the item (3-500 characters)
            </p>
          </div>

          {/* Storage Date */}
          <div>
            <label
              htmlFor="storage_date"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Storage Date *
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                id="storage_date"
                name="storage_date"
                type="date"
                required
                value={formData.storage_date}
                onChange={handleChange}
                max={new Date().toISOString().split('T')[0]} // Cannot be future date
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.storage_date ? "border-red-300" : "border-gray-300"
                }`}
                disabled={isSubmitting}
              />
            </div>
            {errors.storage_date && (
              <p className="mt-1 text-sm text-red-600">{errors.storage_date}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              When was the item stored? (Cannot be in the future)
            </p>
          </div>

          {/* Notes */}
          <div>
            <label
              htmlFor="notes"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Notes
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                id="notes"
                name="notes"
                rows={4}
                value={formData.notes}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.notes ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Additional notes about the storage (optional)"
                disabled={isSubmitting}
              />
            </div>
            {errors.notes && (
              <p className="mt-1 text-sm text-red-600">{errors.notes}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Any additional information about the storage (max 1000 characters)
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditMode ? "Updating..." : "Storing..."}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditMode ? "Update Storage" : "Store Item"}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerStorageFormModal;
