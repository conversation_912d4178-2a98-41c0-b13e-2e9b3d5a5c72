"use client";

import React from "react";
import {
  X,
  Activity,
  User,
  Package,
  Calendar,
  Hash,
  DollarSign,
  CreditCard,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RotateCcw,
} from "lucide-react";
import {
  ServiceConsumption,
  getPaymentStatusLabel,
  getPaymentStatusColor,
  isPaymentPending,
  isPaymentCompleted,
  formatCurrency,
} from "@/types/service-consumption";

interface ServiceConsumptionDetailsModalProps {
  serviceConsumption: ServiceConsumption;
  onClose: () => void;
}

const ServiceConsumptionDetailsModal: React.FC<ServiceConsumptionDetailsModalProps> = ({
  serviceConsumption,
  onClose,
}) => {
  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-600" />;
      case 'paid':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-gray-600" />;
      case 'refunded':
        return <RotateCcw className="w-5 h-5 text-red-600" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-gray-600" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <Activity className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Service Consumption Details
              </h2>
              <p className="text-sm text-gray-600">
                View detailed information about this service consumption
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Payment Status Banner */}
          <div
            className={`p-4 rounded-lg border ${
              isPaymentPending(serviceConsumption)
                ? "bg-yellow-50 border-yellow-200"
                : isPaymentCompleted(serviceConsumption)
                ? "bg-green-50 border-green-200"
                : serviceConsumption.payment_status === 'cancelled'
                ? "bg-gray-50 border-gray-200"
                : "bg-red-50 border-red-200"
            }`}
          >
            <div className="flex items-center">
              {getPaymentStatusIcon(serviceConsumption.payment_status)}
              <div className="ml-3">
                <h3
                  className={`text-sm font-medium ${
                    isPaymentPending(serviceConsumption)
                      ? "text-yellow-800"
                      : isPaymentCompleted(serviceConsumption)
                      ? "text-green-800"
                      : serviceConsumption.payment_status === 'cancelled'
                      ? "text-gray-800"
                      : "text-red-800"
                  }`}
                >
                  Payment Status: {getPaymentStatusLabel(serviceConsumption.payment_status)}
                </h3>
                <p
                  className={`text-sm ${
                    isPaymentPending(serviceConsumption)
                      ? "text-yellow-700"
                      : isPaymentCompleted(serviceConsumption)
                      ? "text-green-700"
                      : serviceConsumption.payment_status === 'cancelled'
                      ? "text-gray-700"
                      : "text-red-700"
                  }`}
                >
                  {isPaymentPending(serviceConsumption) && "Payment is pending for this consumption"}
                  {isPaymentCompleted(serviceConsumption) && serviceConsumption.payment_date && 
                    `Payment completed on ${new Date(serviceConsumption.payment_date).toLocaleDateString()}`}
                  {serviceConsumption.payment_status === 'cancelled' && "This consumption has been cancelled"}
                  {serviceConsumption.payment_status === 'refunded' && "Payment has been refunded"}
                </p>
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <User className="w-5 h-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Customer Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Name</p>
                <p className="text-sm text-gray-900">
                  {serviceConsumption.customer.first_name} {serviceConsumption.customer.last_name}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Email</p>
                <p className="text-sm text-gray-900">
                  {serviceConsumption.customer.email || 'Not provided'}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Phone</p>
                <p className="text-sm text-gray-900">
                  {serviceConsumption.customer.phone_number || 'Not provided'}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Customer ID</p>
                <p className="text-sm text-gray-900 font-mono">
                  {serviceConsumption.customer_id}
                </p>
              </div>
            </div>
          </div>

          {/* Service Information */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <Package className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Service Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Service Name</p>
                <p className="text-sm text-gray-900 font-medium">
                  {serviceConsumption.service.name}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Service ID</p>
                <p className="text-sm text-gray-900 font-mono">
                  {serviceConsumption.service_id}
                </p>
              </div>
              {serviceConsumption.service.description && (
                <div className="md:col-span-2">
                  <p className="text-sm font-medium text-gray-600">Description</p>
                  <p className="text-sm text-gray-900">
                    {serviceConsumption.service.description}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Consumption Details */}
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <Activity className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Consumption Details</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Consumption Date</p>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                  <p className="text-sm text-gray-900">
                    {new Date(serviceConsumption.consumption_date).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Quantity</p>
                <div className="flex items-center">
                  <Hash className="w-4 h-4 text-gray-500 mr-2" />
                  <p className="text-sm text-gray-900">
                    {serviceConsumption.quantity}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Unit Price</p>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 text-gray-500 mr-2" />
                  <p className="text-sm text-gray-900">
                    {formatCurrency(serviceConsumption.unit_price)}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Amount</p>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 text-gray-500 mr-2" />
                  <p className="text-lg font-bold text-green-600">
                    {formatCurrency(serviceConsumption.total_amount)}
                  </p>
                </div>
              </div>
            </div>
            <div className="mt-3 pt-3 border-t border-green-200">
              <p className="text-xs text-green-700">
                Calculation: {serviceConsumption.quantity} × {formatCurrency(serviceConsumption.unit_price)} = {formatCurrency(serviceConsumption.total_amount)}
              </p>
            </div>
          </div>

          {/* Payment Information */}
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <CreditCard className="w-5 h-5 text-purple-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Payment Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Payment Status</p>
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(
                    serviceConsumption.payment_status
                  )}`}
                >
                  {getPaymentStatusLabel(serviceConsumption.payment_status)}
                </span>
              </div>
              {serviceConsumption.payment_date && (
                <div>
                  <p className="text-sm font-medium text-gray-600">Payment Date</p>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                    <p className="text-sm text-gray-900">
                      {new Date(serviceConsumption.payment_date).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          {serviceConsumption.notes && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <FileText className="w-5 h-5 text-gray-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Notes</h3>
              </div>
              <p className="text-sm text-gray-900 whitespace-pre-wrap">
                {serviceConsumption.notes}
              </p>
            </div>
          )}

          {/* Record Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <Clock className="w-5 h-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Record Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Consumption ID</p>
                <p className="text-sm text-gray-900 font-mono">
                  {serviceConsumption.consumption_id}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Created By</p>
                <p className="text-sm text-gray-900">
                  {serviceConsumption.created_by}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Created At</p>
                <p className="text-sm text-gray-900">
                  {new Date(serviceConsumption.created_at).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Last Updated</p>
                <p className="text-sm text-gray-900">
                  {new Date(serviceConsumption.updated_at).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServiceConsumptionDetailsModal;
