"use client";

import React from "react";
import Link from "next/link";
import AttendanceSummaryReport from "./AttendanceSummaryReport";
import { BarChart3 } from "lucide-react";

const ReportsContent: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Reports</h1>
        <div className="flex items-center">
          <BarChart3 className="h-6 w-6 text-primary mr-2" />
          <span className="text-sm text-gray-600">
            Generate comprehensive reports
          </span>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">
          Dashboard
        </Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Reports</span>
      </div>

      {/* Attendance Summary Report */}
      <AttendanceSummaryReport />
    </div>
  );
};

export default ReportsContent;
