"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Copy,
  RefreshCw,
  DollarSign,
  TrendingUp,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { generateBreadcrumbs } from "@/lib/breadcrumb-utils";
import {
  getServicePrices,
  duplicateServicePrice,
  getServicePriceStatistics,
} from "@/lib/service-price";
import { getServices } from "@/lib/service";
import {
  ServicePrice,
  ServicePriceFilters,
  formatPrice,
  formatPriceAmount,
  isCurrentPrice,
  isPriceExpired,
  isPriceFuture,
  getPriceStatusLabel,
  getPriceStatusColor,
  SERVICE_PRICE_SORT_OPTIONS,
} from "@/types/service-price";
import { Service } from "@/types/service";
import ServicePriceFormModal from "@/components/service-price/ServicePriceFormModal";
import ServicePriceDetailsModal from "@/components/service-price/ServicePriceDetailsModal";

interface ServicePricesContentProps {}

const ServicePricesContent: React.FC<ServicePricesContentProps> = () => {
  const { user } = useAuth();
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, user?.role);

  // State management
  const [servicePrices, setServicePrices] = useState<ServicePrice[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<ServicePriceFilters>({});
  const [sortBy, setSortBy] = useState<string>("effective_from_desc");
  const [statistics, setStatistics] = useState({
    total: 0,
    current: 0,
    expired: 0,
    future: 0,
    services_with_prices: 0,
  });

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedServicePrice, setSelectedServicePrice] =
    useState<ServicePrice | null>(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Load data
  useEffect(() => {
    loadData();
  }, [filters, searchTerm]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [pricesResponse, servicesResponse] = await Promise.all([
        getServicePrices({
          ...filters,
          search: searchTerm || undefined,
        }),
        getServices({ is_active: true }), // Only load active services
      ]);

      setServicePrices(pricesResponse.extend.service_prices);
      setServices(servicesResponse.extend.services);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load service prices. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const stats = await getServicePriceStatistics();
      setStatistics(stats);
    } catch (err) {
      console.error("Error loading statistics:", err);
    }
  };

  // Load statistics on component mount
  useEffect(() => {
    loadStatistics();
  }, []);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (newFilters: Partial<ServicePriceFilters>) => {
    setFilters({ ...filters, ...newFilters });
    setCurrentPage(1);
  };

  // Handle duplicate service price
  const handleDuplicateServicePrice = async (servicePrice: ServicePrice) => {
    try {
      await duplicateServicePrice(servicePrice.price_id);
      await loadData();
      await loadStatistics();
    } catch (error) {
      console.error("Error duplicating service price:", error);
      alert("Failed to duplicate service price. Please try again.");
    }
  };

  // Handle view service price
  const handleViewServicePrice = (servicePrice: ServicePrice) => {
    setSelectedServicePrice(servicePrice);
    setShowDetailsModal(true);
  };

  // Handle edit service price
  const handleEditServicePrice = (servicePrice: ServicePrice) => {
    setSelectedServicePrice(servicePrice);
    setShowEditModal(true);
  };

  // Handle form success
  const handleFormSuccess = async () => {
    await loadData();
    await loadStatistics();
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedServicePrice(null);
  };

  // Sort service prices
  const sortedServicePrices = [...servicePrices].sort((a, b) => {
    switch (sortBy) {
      case "service_name_asc":
        return a.service.name.localeCompare(b.service.name);
      case "service_name_desc":
        return b.service.name.localeCompare(a.service.name);
      case "price_asc":
        return a.price_amount - b.price_amount;
      case "price_desc":
        return b.price_amount - a.price_amount;
      case "effective_from_desc":
        return (
          new Date(b.effective_from).getTime() -
          new Date(a.effective_from).getTime()
        );
      case "effective_from_asc":
        return (
          new Date(a.effective_from).getTime() -
          new Date(b.effective_from).getTime()
        );
      case "status_current":
        const aIsCurrent = isCurrentPrice(a);
        const bIsCurrent = isCurrentPrice(b);
        if (aIsCurrent === bIsCurrent) return 0;
        return bIsCurrent ? 1 : -1;
      default:
        return 0;
    }
  });

  // Paginate service prices
  const totalPages = Math.ceil(sortedServicePrices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedServicePrices = sortedServicePrices.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb items={breadcrumbs} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">
            Service Prices Management
          </h1>
          <p className="mt-1 text-sm text-secondary">
            Manage service pricing with effective dates and currency support
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex gap-3">
          <button
            onClick={() => loadData()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Price
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Prices</p>
              <p className="text-2xl font-bold text-gray-900">
                {statistics.total}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Current Prices
              </p>
              <p className="text-2xl font-bold text-green-600">
                {statistics.current}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Expired Prices
              </p>
              <p className="text-2xl font-bold text-red-600">
                {statistics.expired}
              </p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <XCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Future Prices</p>
              <p className="text-2xl font-bold text-blue-600">
                {statistics.future}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Services with Prices
              </p>
              <p className="text-2xl font-bold text-purple-600">
                {statistics.services_with_prices}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search service prices..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          {/* Service Filter */}
          <div className="sm:w-48">
            <select
              value={filters.service_id || ""}
              onChange={(e) =>
                handleFilterChange({
                  service_id: e.target.value || undefined,
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">All Services</option>
              {services.map((service) => (
                <option key={service.service_id} value={service.service_id}>
                  {service.name}
                </option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div className="sm:w-48">
            <select
              value={filters.is_current?.toString() || ""}
              onChange={(e) =>
                handleFilterChange({
                  is_current:
                    e.target.value === ""
                      ? undefined
                      : e.target.value === "true",
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">All Prices</option>
              <option value="true">Current Only</option>
              <option value="false">Non-Current Only</option>
            </select>
          </div>

          {/* Sort */}
          <div className="sm:w-48">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {SERVICE_PRICE_SORT_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Service Prices List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading service prices...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <p className="text-red-600">{error}</p>
            <button
              onClick={() => loadData()}
              className="mt-2 text-primary hover:text-primary-dark"
            >
              Try again
            </button>
          </div>
        ) : paginatedServicePrices.length === 0 ? (
          <div className="p-8 text-center">
            <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No service prices found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ||
              filters.service_id ||
              filters.is_current !== undefined
                ? "Try adjusting your search or filters."
                : "Get started by creating your first service price."}
            </p>
            {!searchTerm &&
              !filters.service_id &&
              filters.is_current === undefined && (
                <div className="mt-6">
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Price
                  </button>
                </div>
              )}
          </div>
        ) : (
          <>
            {/* Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Service
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Effective Period
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedServicePrices.map((servicePrice) => (
                    <tr
                      key={servicePrice.price_id}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-lg bg-primary-light flex items-center justify-center">
                              <DollarSign className="h-5 w-5 text-primary" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {servicePrice.service.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              ID: {servicePrice.price_id.substring(0, 8)}...
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatPrice(
                            servicePrice.price_amount,
                            servicePrice.currency
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          {servicePrice.currency}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                            {new Date(
                              servicePrice.effective_from
                            ).toLocaleDateString()}
                          </div>
                          {servicePrice.effective_to && (
                            <div className="flex items-center mt-1">
                              <Clock className="h-4 w-4 text-gray-400 mr-1" />
                              {new Date(
                                servicePrice.effective_to
                              ).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriceStatusColor(
                            servicePrice
                          )}`}
                        >
                          {getPriceStatusLabel(servicePrice)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewServicePrice(servicePrice)}
                            className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-100"
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleEditServicePrice(servicePrice)}
                            className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-100"
                            title="Edit Price"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() =>
                              handleDuplicateServicePrice(servicePrice)
                            }
                            className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-100"
                            title="Duplicate Price"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() =>
                      setCurrentPage(Math.min(totalPages, currentPage + 1))
                    }
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing{" "}
                      <span className="font-medium">{startIndex + 1}</span> to{" "}
                      <span className="font-medium">
                        {Math.min(
                          startIndex + itemsPerPage,
                          sortedServicePrices.length
                        )}
                      </span>{" "}
                      of{" "}
                      <span className="font-medium">
                        {sortedServicePrices.length}
                      </span>{" "}
                      results
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() =>
                          setCurrentPage(Math.max(1, currentPage - 1))
                        }
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronLeft className="h-5 w-5" />
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                        (page) => (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              page === currentPage
                                ? "z-10 bg-primary border-primary text-white"
                                : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                            }`}
                          >
                            {page}
                          </button>
                        )
                      )}
                      <button
                        onClick={() =>
                          setCurrentPage(Math.min(totalPages, currentPage + 1))
                        }
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronRight className="h-5 w-5" />
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Modals */}
      {showCreateModal && (
        <ServicePriceFormModal
          services={services}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowCreateModal(false)}
        />
      )}

      {showEditModal && selectedServicePrice && (
        <ServicePriceFormModal
          servicePrice={selectedServicePrice}
          services={services}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setShowEditModal(false);
            setSelectedServicePrice(null);
          }}
        />
      )}

      {showDetailsModal && selectedServicePrice && (
        <ServicePriceDetailsModal
          servicePrice={selectedServicePrice}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedServicePrice(null);
          }}
        />
      )}
    </div>
  );
};

export default ServicePricesContent;
