"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Copy,
  Trash2,
  RefreshCw,
  Package,
  MapPin,
  BarChart3,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { generateBreadcrumbs } from "@/lib/breadcrumb-utils";
import {
  getStorageLocations,
  deleteStorageLocation,
  duplicateStorageLocation,
  getStorageLocationStatistics,
  toggleStorageLocationAvailability,
} from "@/lib/storage-location";
import {
  StorageLocation,
  StorageLocationFilters,
  formatLocationName,
  getLocationTypeLabel,
  getAvailabilityLabel,
  getAvailabilityColor,
  getCapacityUsagePercentage,
  getCapacityStatusColor,
  getCapacityStatusLabel,
  hasAvailableCapacity,
  getAvailableCapacity,
  isLocationActive,
  STORAGE_LOCATION_TYPES,
  STORAGE_LOCATION_SORT_OPTIONS,
  StorageLocationStatistics,
} from "@/types/storage-location";
import StorageLocationFormModal from "@/components/storage-location/StorageLocationFormModal";
import StorageLocationDetailsModal from "@/components/storage-location/StorageLocationDetailsModal";

interface StorageLocationsContentProps {}

const StorageLocationsContent: React.FC<StorageLocationsContentProps> = () => {
  const { user } = useAuth();
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, user?.role);

  // State management
  const [storageLocations, setStorageLocations] = useState<StorageLocation[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<StorageLocationFilters>({});
  const [sortBy, setSortBy] = useState<string>("name_asc");
  const [statistics, setStatistics] = useState<StorageLocationStatistics>({
    total: 0,
    available: 0,
    unavailable: 0,
    total_capacity: 0,
    total_usage: 0,
    by_type: {},
  });

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedStorageLocation, setSelectedStorageLocation] =
    useState<StorageLocation | null>(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Load data
  useEffect(() => {
    loadData();
  }, [filters, searchTerm]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getStorageLocations({
        ...filters,
        search: searchTerm || undefined,
      });

      setStorageLocations(response.extend.storage_locations);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load storage locations. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const stats = await getStorageLocationStatistics();
      setStatistics(stats);
    } catch (err) {
      console.error("Error loading statistics:", err);
    }
  };

  // Load statistics on component mount
  useEffect(() => {
    loadStatistics();
  }, []);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (newFilters: Partial<StorageLocationFilters>) => {
    setFilters({ ...filters, ...newFilters });
    setCurrentPage(1);
  };

  // Handle delete storage location
  const handleDeleteStorageLocation = async (
    storageLocation: StorageLocation
  ) => {
    if (storageLocation.current_usage > 0) {
      alert(
        "Cannot delete storage location that is currently in use. Please empty it first."
      );
      return;
    }

    const confirmed = confirm(
      `Are you sure you want to delete "${storageLocation.location_name}"? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      await deleteStorageLocation(storageLocation.location_id);
      await loadData();
      await loadStatistics();
    } catch (error) {
      console.error("Error deleting storage location:", error);
      alert("Failed to delete storage location. Please try again.");
    }
  };

  // Handle duplicate storage location
  const handleDuplicateStorageLocation = async (
    storageLocation: StorageLocation
  ) => {
    try {
      await duplicateStorageLocation(storageLocation.location_id);
      await loadData();
      await loadStatistics();
    } catch (error) {
      console.error("Error duplicating storage location:", error);
      alert("Failed to duplicate storage location. Please try again.");
    }
  };

  // Handle toggle availability
  const handleToggleAvailability = async (storageLocation: StorageLocation) => {
    try {
      await toggleStorageLocationAvailability(storageLocation.location_id);
      await loadData();
      await loadStatistics();
    } catch (error) {
      console.error("Error toggling availability:", error);
      alert("Failed to update availability. Please try again.");
    }
  };

  // Handle view storage location
  const handleViewStorageLocation = (storageLocation: StorageLocation) => {
    setSelectedStorageLocation(storageLocation);
    setShowDetailsModal(true);
  };

  // Handle edit storage location
  const handleEditStorageLocation = (storageLocation: StorageLocation) => {
    setSelectedStorageLocation(storageLocation);
    setShowEditModal(true);
  };

  // Handle form success
  const handleFormSuccess = async () => {
    await loadData();
    await loadStatistics();
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedStorageLocation(null);
  };

  // Sort storage locations
  const sortedStorageLocations = [...storageLocations].sort((a, b) => {
    switch (sortBy) {
      case "name_asc":
        return a.location_name.localeCompare(b.location_name);
      case "name_desc":
        return b.location_name.localeCompare(a.location_name);
      case "type_asc":
        return a.location_type.localeCompare(b.location_type);
      case "type_desc":
        return b.location_type.localeCompare(a.location_type);
      case "capacity_asc":
        return a.capacity - b.capacity;
      case "capacity_desc":
        return b.capacity - a.capacity;
      case "usage_asc":
        return a.current_usage - b.current_usage;
      case "usage_desc":
        return b.current_usage - a.current_usage;
      case "available_first":
        if (a.is_available === b.is_available) return 0;
        return a.is_available ? -1 : 1;
      case "created_desc":
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      case "created_asc":
        return (
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
      default:
        return 0;
    }
  });

  // Paginate storage locations
  const totalPages = Math.ceil(sortedStorageLocations.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedStorageLocations = sortedStorageLocations.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb items={breadcrumbs} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">
            Storage Locations Management
          </h1>
          <p className="mt-1 text-sm text-secondary">
            Manage storage locations and track capacity usage
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex gap-3">
          <button
            onClick={() => loadData()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Location
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Total Locations
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {statistics.total}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <MapPin className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Available</p>
              <p className="text-2xl font-bold text-green-600">
                {statistics.available}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Total Capacity
              </p>
              <p className="text-2xl font-bold text-purple-600">
                {statistics.total_capacity}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Package className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Usage Rate</p>
              <p className="text-2xl font-bold text-orange-600">
                {statistics.total_capacity > 0
                  ? Math.round(
                      (statistics.total_usage / statistics.total_capacity) * 100
                    )
                  : 0}
                %
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <BarChart3 className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search storage locations..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          {/* Type Filter */}
          <div className="sm:w-48">
            <select
              value={filters.location_type || ""}
              onChange={(e) =>
                handleFilterChange({
                  location_type: e.target.value || undefined,
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">All Types</option>
              {STORAGE_LOCATION_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Availability Filter */}
          <div className="sm:w-48">
            <select
              value={filters.is_available?.toString() || ""}
              onChange={(e) =>
                handleFilterChange({
                  is_available:
                    e.target.value === ""
                      ? undefined
                      : e.target.value === "true",
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">All Locations</option>
              <option value="true">Available Only</option>
              <option value="false">Unavailable Only</option>
            </select>
          </div>

          {/* Capacity Filter */}
          <div className="sm:w-48">
            <select
              value={filters.has_capacity?.toString() || ""}
              onChange={(e) =>
                handleFilterChange({
                  has_capacity:
                    e.target.value === ""
                      ? undefined
                      : e.target.value === "true",
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">All Capacities</option>
              <option value="true">Has Capacity</option>
              <option value="false">Full</option>
            </select>
          </div>

          {/* Sort */}
          <div className="sm:w-48">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {STORAGE_LOCATION_SORT_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Storage Locations List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading storage locations...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <p className="text-red-600">{error}</p>
            <button
              onClick={() => loadData()}
              className="mt-2 text-primary hover:text-primary-dark"
            >
              Try again
            </button>
          </div>
        ) : paginatedStorageLocations.length === 0 ? (
          <div className="p-8 text-center">
            <MapPin className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No storage locations found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ||
              filters.location_type ||
              filters.is_available !== undefined ||
              filters.has_capacity !== undefined
                ? "Try adjusting your search or filters."
                : "Get started by creating your first storage location."}
            </p>
            {!searchTerm &&
              !filters.location_type &&
              filters.is_available === undefined &&
              filters.has_capacity === undefined && (
                <div className="mt-6">
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Location
                  </button>
                </div>
              )}
          </div>
        ) : (
          <>
            {/* Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Capacity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Usage
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedStorageLocations.map((storageLocation) => (
                    <tr
                      key={storageLocation.location_id}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-lg bg-primary-light flex items-center justify-center">
                              <MapPin className="h-5 w-5 text-primary" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {formatLocationName(storageLocation)}
                            </div>
                            <div className="text-sm text-gray-500">
                              ID: {storageLocation.location_id.substring(0, 8)}
                              ...
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {getLocationTypeLabel(storageLocation.location_type)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {storageLocation.capacity}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-1">
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-900">
                                {storageLocation.current_usage} /{" "}
                                {storageLocation.capacity}
                              </span>
                              <span className="text-gray-500">
                                {getCapacityUsagePercentage(storageLocation)}%
                              </span>
                            </div>
                            <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${getCapacityStatusColor(
                                  storageLocation
                                )}`}
                                style={{
                                  width: `${getCapacityUsagePercentage(
                                    storageLocation
                                  )}%`,
                                }}
                              ></div>
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {getCapacityStatusLabel(storageLocation)}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getAvailabilityColor(
                            storageLocation.is_available
                          )}`}
                        >
                          {getAvailabilityLabel(storageLocation.is_available)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() =>
                              handleViewStorageLocation(storageLocation)
                            }
                            className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-100"
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() =>
                              handleEditStorageLocation(storageLocation)
                            }
                            className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-100"
                            title="Edit Location"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() =>
                              handleDuplicateStorageLocation(storageLocation)
                            }
                            className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-100"
                            title="Duplicate Location"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() =>
                              handleToggleAvailability(storageLocation)
                            }
                            className={`p-1 rounded-full ${
                              storageLocation.is_available
                                ? "text-yellow-600 hover:text-yellow-900 hover:bg-yellow-100"
                                : "text-green-600 hover:text-green-900 hover:bg-green-100"
                            }`}
                            title={
                              storageLocation.is_available
                                ? "Mark Unavailable"
                                : "Mark Available"
                            }
                          >
                            {storageLocation.is_available ? (
                              <XCircle className="h-4 w-4" />
                            ) : (
                              <CheckCircle className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() =>
                              handleDeleteStorageLocation(storageLocation)
                            }
                            className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-100"
                            title="Delete Location"
                            disabled={storageLocation.current_usage > 0}
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() =>
                      setCurrentPage(Math.min(totalPages, currentPage + 1))
                    }
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing{" "}
                      <span className="font-medium">{startIndex + 1}</span> to{" "}
                      <span className="font-medium">
                        {Math.min(
                          startIndex + itemsPerPage,
                          sortedStorageLocations.length
                        )}
                      </span>{" "}
                      of{" "}
                      <span className="font-medium">
                        {sortedStorageLocations.length}
                      </span>{" "}
                      results
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() =>
                          setCurrentPage(Math.max(1, currentPage - 1))
                        }
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronLeft className="h-5 w-5" />
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                        (page) => (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              page === currentPage
                                ? "z-10 bg-primary border-primary text-white"
                                : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                            }`}
                          >
                            {page}
                          </button>
                        )
                      )}
                      <button
                        onClick={() =>
                          setCurrentPage(Math.min(totalPages, currentPage + 1))
                        }
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronRight className="h-5 w-5" />
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Modals */}
      {showCreateModal && (
        <StorageLocationFormModal
          onSuccess={handleFormSuccess}
          onCancel={() => setShowCreateModal(false)}
        />
      )}

      {showEditModal && selectedStorageLocation && (
        <StorageLocationFormModal
          storageLocation={selectedStorageLocation}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setShowEditModal(false);
            setSelectedStorageLocation(null);
          }}
        />
      )}

      {showDetailsModal && selectedStorageLocation && (
        <StorageLocationDetailsModal
          storageLocation={selectedStorageLocation}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedStorageLocation(null);
          }}
        />
      )}
    </div>
  );
};

export default StorageLocationsContent;
