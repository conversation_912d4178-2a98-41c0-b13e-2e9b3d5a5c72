"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { generateBreadcrumbs } from "@/lib/breadcrumb-utils";
import {
  UserCheck,
  Activity,
  Award,
  Gift,
  Settings,
  DollarSign,
  MapPin,
  Package,
  BarChart3,
  Users,
  ArrowRight,
} from "lucide-react";

interface CustomerManagementDashboardProps {}

const CustomerManagementDashboard: React.FC<
  CustomerManagementDashboardProps
> = () => {
  const { user } = useAuth();
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, user?.role);

  const managementFeatures = [
    {
      title: "Customer Records",
      description: "Manage customer profiles, registration, and information",
      href: "/dashboard/hr/customers",
      icon: UserCheck,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
      borderColor: "border-emerald-200",
      hoverColor: "hover:bg-emerald-100",
      badge: null,
    },
    {
      title: "Customer Visits",
      description: "Track customer visits, check-ins, and visit history",
      href: "/dashboard/hr/customer-visits",
      icon: Activity,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      hoverColor: "hover:bg-blue-100",
      badge: null,
    },
    {
      title: "Services Management",
      description: "Manage available services and service offerings",
      href: "/dashboard/hr/customer-management/services",
      icon: Settings,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      hoverColor: "hover:bg-purple-100",
      badge: "NEW",
    },
    {
      title: "Service Pricing",
      description: "Manage service prices with effective dates and currency",
      href: "/dashboard/hr/customer-management/service-prices",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      hoverColor: "hover:bg-green-100",
      badge: "NEW",
    },
    {
      title: "Storage Locations",
      description: "Manage storage lockers and location availability",
      href: "/dashboard/hr/customer-management/storage-locations",
      icon: MapPin,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
      hoverColor: "hover:bg-orange-100",
      badge: "NEW",
    },
    {
      title: "Customer Storage",
      description: "Track customer belongings storage and retrieval",
      href: "/dashboard/hr/customer-management/customer-storage",
      icon: Package,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
      borderColor: "border-indigo-200",
      hoverColor: "hover:bg-indigo-100",
      badge: "NEW",
    },
    {
      title: "Service Consumption",
      description: "Track and manage customer service usage and billing",
      href: "/dashboard/hr/customer-management/service-consumption",
      icon: BarChart3,
      color: "text-teal-600",
      bgColor: "bg-teal-50",
      borderColor: "border-teal-200",
      hoverColor: "hover:bg-teal-100",
      badge: "NEW",
    },
    {
      title: "Promotions",
      description: "Create and manage promotional campaigns and offers",
      href: "/dashboard/hr/promotions",
      icon: Award,
      color: "text-pink-600",
      bgColor: "bg-pink-50",
      borderColor: "border-pink-200",
      hoverColor: "hover:bg-pink-100",
      badge: null,
    },
    {
      title: "Loyalty Management",
      description: "Manage customer rewards, points, and loyalty programs",
      href: "/dashboard/hr/loyalty",
      icon: Gift,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200",
      hoverColor: "hover:bg-yellow-100",
      badge: null,
    },
  ];

  const quickActions = [
    {
      title: "Record Service Usage",
      description: "Quickly record a customer's service consumption",
      href: "/dashboard/hr/customer-management/service-consumption",
      icon: Activity,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
    },
    {
      title: "Manage Services",
      description: "Add or update available services",
      href: "/dashboard/hr/customer-management/services",
      icon: Settings,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
    },
    {
      title: "Customer Storage",
      description: "Track customer belongings and storage",
      href: "/dashboard/hr/customer-management/customer-storage",
      icon: Package,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
    },
    {
      title: "View All Customers",
      description: "Access complete customer database",
      href: "/dashboard/hr/customers",
      icon: Users,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb items={breadcrumbs} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">
            Customer Management
          </h1>
          <p className="mt-1 text-sm text-secondary">
            Comprehensive customer relationship and service management platform
          </p>
        </div>
      </div>

      {/* Welcome Message */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-center">
          <div className="p-3 bg-blue-100 rounded-lg mr-4">
            <Users className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Welcome to Customer Management
            </h2>
            <p className="text-gray-600 mt-1">
              Manage all aspects of your customer relationships, services, and
              operations from this centralized hub.
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickActions.map((action, index) => {
          const IconComponent = action.icon;
          return (
            <Link
              key={index}
              href={action.href}
              className={`${action.bgColor} ${action.borderColor} border p-6 rounded-lg hover:shadow-md transition-all duration-200 group`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <IconComponent className={`w-5 h-5 ${action.color} mr-2`} />
                    <h3 className="font-medium text-gray-900 group-hover:text-gray-700">
                      {action.title}
                    </h3>
                  </div>
                  <p className="text-sm text-gray-600 group-hover:text-gray-500">
                    {action.description}
                  </p>
                </div>
                <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
              </div>
            </Link>
          );
        })}
      </div>

      {/* Management Features Grid */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Management Features
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {managementFeatures.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <Link
                key={index}
                href={feature.href}
                className={`relative p-6 border ${feature.borderColor} rounded-lg ${feature.hoverColor} transition-colors text-center group`}
              >
                {feature.badge && (
                  <div className="absolute top-3 right-3">
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-green-100 to-blue-100 text-green-800">
                      {feature.badge}
                    </span>
                  </div>
                )}
                <div
                  className={`${feature.bgColor} p-3 rounded-lg inline-block mb-4`}
                >
                  <IconComponent className={`w-8 h-8 ${feature.color}`} />
                </div>
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-xs text-gray-500">{feature.description}</p>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default CustomerManagementDashboard;
