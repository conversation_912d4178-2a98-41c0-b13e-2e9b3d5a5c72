"use client";

import React from "react";
import {
  X,
  DollarSign,
  Calendar,
  User,
  CheckCircle,
  XCircle,
  Clock,
  Settings,
  TrendingUp,
  AlertTriangle,
} from "lucide-react";
import {
  ServicePrice,
  formatPrice,
  formatPriceAmount,
  isCurrentPrice,
  isPriceExpired,
  isPriceFuture,
  getPriceStatusLabel,
  getPriceStatusColor,
} from "@/types/service-price";

interface ServicePriceDetailsModalProps {
  servicePrice: ServicePrice;
  onClose: () => void;
}

const ServicePriceDetailsModal: React.FC<ServicePriceDetailsModalProps> = ({
  servicePrice,
  onClose,
}) => {
  const isCurrent = isCurrentPrice(servicePrice);
  const isExpired = isPriceExpired(servicePrice);
  const isFuture = isPriceFuture(servicePrice);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <DollarSign className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Service Price Details
              </h2>
              <p className="text-sm text-gray-600">
                {servicePrice.service.name} - {formatPrice(servicePrice.price_amount, servicePrice.currency)}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status Banner */}
          <div
            className={`p-4 rounded-lg border ${
              isCurrent
                ? "bg-green-50 border-green-200"
                : isExpired
                ? "bg-red-50 border-red-200"
                : isFuture
                ? "bg-blue-50 border-blue-200"
                : "bg-gray-50 border-gray-200"
            }`}
          >
            <div className="flex items-center">
              {isCurrent ? (
                <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
              ) : isExpired ? (
                <XCircle className="w-5 h-5 text-red-600 mr-3" />
              ) : isFuture ? (
                <Clock className="w-5 h-5 text-blue-600 mr-3" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-gray-600 mr-3" />
              )}
              <div>
                <h3
                  className={`font-medium ${
                    isCurrent
                      ? "text-green-800"
                      : isExpired
                      ? "text-red-800"
                      : isFuture
                      ? "text-blue-800"
                      : "text-gray-800"
                  }`}
                >
                  {getPriceStatusLabel(servicePrice)}
                </h3>
                <p
                  className={`text-sm mt-1 ${
                    isCurrent
                      ? "text-green-700"
                      : isExpired
                      ? "text-red-700"
                      : isFuture
                      ? "text-blue-700"
                      : "text-gray-700"
                  }`}
                >
                  {isCurrent
                    ? "This price is currently active and being used for billing."
                    : isExpired
                    ? "This price has expired and is no longer in use."
                    : isFuture
                    ? "This price will become active in the future."
                    : "This price is not currently active."}
                </p>
              </div>
            </div>
          </div>

          {/* Service Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Service Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Service Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Service Name
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Settings className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-900">
                    {servicePrice.service.name}
                  </span>
                </div>
              </div>

              {/* Service ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Service ID
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-900 font-mono">
                    {servicePrice.service_id}
                  </span>
                </div>
              </div>

              {/* Service Description */}
              {servicePrice.service.description && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Service Description
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-900">
                      {servicePrice.service.description}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Price Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Price Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Price Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price Amount
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <DollarSign className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-lg font-semibold text-gray-900">
                      {formatPrice(servicePrice.price_amount, servicePrice.currency)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatPriceAmount(servicePrice.price_amount)} {servicePrice.currency}
                    </div>
                  </div>
                </div>
              </div>

              {/* Currency */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <TrendingUp className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-900">
                    {servicePrice.currency}
                  </span>
                </div>
              </div>

              {/* Price ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price ID
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-900 font-mono">
                    {servicePrice.price_id}
                  </span>
                </div>
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriceStatusColor(
                      servicePrice
                    )}`}
                  >
                    {getPriceStatusLabel(servicePrice)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Effective Period */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Effective Period
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Effective From */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Effective From
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-900">
                      {new Date(servicePrice.effective_from).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(servicePrice.effective_from).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Effective To */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Effective To
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Clock className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    {servicePrice.effective_to ? (
                      <>
                        <div className="text-sm text-gray-900">
                          {new Date(servicePrice.effective_to).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(servicePrice.effective_to).toLocaleTimeString()}
                        </div>
                      </>
                    ) : (
                      <div className="text-sm text-gray-500">No expiration date</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Record Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Created At */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Created At
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-900">
                      {new Date(servicePrice.created_at).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(servicePrice.created_at).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Updated At */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Updated
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Clock className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-900">
                      {new Date(servicePrice.updated_at).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(servicePrice.updated_at).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Created By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Created By
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <User className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-900 font-mono">
                    {servicePrice.created_by.substring(0, 8)}...
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-4 h-4 text-blue-600" />
                </div>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-blue-900">
                  Price Management
                </h4>
                <p className="text-sm text-blue-700 mt-1">
                  This price record tracks the cost of the service for the specified
                  effective period. You can create multiple price records for the same
                  service to handle price changes over time.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServicePriceDetailsModal;
