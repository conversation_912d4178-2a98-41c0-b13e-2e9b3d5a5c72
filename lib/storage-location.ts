/**
 * Storage Location API Service
 * Handles all API calls for storage location management functionality
 */

import { apiGet, apiPost, apiPatch, apiDelete } from './api';
import { getAccessToken } from './auth';
import {
  StorageLocation,
  CreateStorageLocationRequest,
  UpdateStorageLocationRequest,
  StorageLocationResponse,
  StorageLocationsListResponse,
  SingleStorageLocationResponse,
  DeleteStorageLocationResponse,
  StorageLocationFilters,
  StorageLocationStatistics,
  BulkUpdateStorageLocationRequest,
} from '@/types/storage-location';

/**
 * Create a new storage location
 */
export const createStorageLocation = async (
  locationData: CreateStorageLocationRequest
): Promise<StorageLocationResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiPost<StorageLocationResponse>('api/storage-locations', locationData, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
};

/**
 * Get storage location by ID
 */
export const getStorageLocationById = async (locationId: string): Promise<SingleStorageLocationResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiGet<SingleStorageLocationResponse>(`api/storage-locations/${locationId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get all storage locations
 */
export const getStorageLocations = async (filters?: StorageLocationFilters): Promise<StorageLocationsListResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  // Build query parameters
  const params = new URLSearchParams();
  
  if (filters) {
    if (filters.location_type) {
      params.append('location_type', filters.location_type);
    }
    if (filters.is_available !== undefined) {
      params.append('is_available', filters.is_available.toString());
    }
    if (filters.has_capacity !== undefined) {
      params.append('has_capacity', filters.has_capacity.toString());
    }
    if (filters.search) {
      params.append('search', filters.search);
    }
    if (filters.page) {
      params.append('page', filters.page.toString());
    }
    if (filters.limit) {
      params.append('limit', filters.limit.toString());
    }
  }

  const queryString = params.toString();
  const url = queryString ? `api/storage-locations?${queryString}` : 'api/storage-locations';

  return apiGet<StorageLocationsListResponse>(url, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Update storage location
 */
export const updateStorageLocation = async (
  locationId: string,
  locationData: UpdateStorageLocationRequest
): Promise<StorageLocationResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiPatch<StorageLocationResponse>(`api/storage-locations/${locationId}`, locationData, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
};

/**
 * Delete storage location
 */
export const deleteStorageLocation = async (locationId: string): Promise<DeleteStorageLocationResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiDelete<DeleteStorageLocationResponse>(`api/storage-locations/${locationId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get available storage locations only
 */
export const getAvailableStorageLocations = async (
  filters?: Omit<StorageLocationFilters, 'is_available'>
): Promise<StorageLocationsListResponse> => {
  return getStorageLocations({
    ...filters,
    is_available: true,
  });
};

/**
 * Get storage locations by type
 */
export const getStorageLocationsByType = async (
  locationType: string,
  filters?: Omit<StorageLocationFilters, 'location_type'>
): Promise<StorageLocationsListResponse> => {
  return getStorageLocations({
    ...filters,
    location_type: locationType,
  });
};

/**
 * Get storage locations with available capacity
 */
export const getStorageLocationsWithCapacity = async (
  filters?: Omit<StorageLocationFilters, 'has_capacity'>
): Promise<StorageLocationsListResponse> => {
  return getStorageLocations({
    ...filters,
    has_capacity: true,
  });
};

/**
 * Search storage locations
 */
export const searchStorageLocations = async (
  searchTerm: string,
  filters?: Omit<StorageLocationFilters, 'search'>
): Promise<StorageLocationsListResponse> => {
  return getStorageLocations({
    ...filters,
    search: searchTerm,
  });
};

/**
 * Get storage locations with pagination
 */
export const getStorageLocationsPaginated = async (
  page: number = 1,
  limit: number = 10,
  filters?: Omit<StorageLocationFilters, 'page' | 'limit'>
): Promise<StorageLocationsListResponse> => {
  return getStorageLocations({
    ...filters,
    page,
    limit,
  });
};

/**
 * Duplicate storage location (create a copy with modified name)
 */
export const duplicateStorageLocation = async (
  locationId: string,
  newName?: string
): Promise<StorageLocationResponse> => {
  // First get the original storage location
  const originalResponse = await getStorageLocationById(locationId);
  const originalLocation = originalResponse.extend.storage_location;

  // Create a new storage location based on the original
  const newLocationData: CreateStorageLocationRequest = {
    location_name: newName || `${originalLocation.location_name} (Copy)`,
    location_type: originalLocation.location_type,
    capacity: originalLocation.capacity,
    description: originalLocation.description || undefined,
  };

  return createStorageLocation(newLocationData);
};

/**
 * Bulk update storage locations
 */
export const bulkUpdateStorageLocations = async (
  bulkData: BulkUpdateStorageLocationRequest
): Promise<{ success: number; failed: number; errors: string[] }> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  // For now, we'll implement this as individual updates
  // In a real implementation, this would be a single API call
  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[],
  };

  for (const locationId of bulkData.location_ids) {
    try {
      await updateStorageLocation(locationId, bulkData.updates);
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Failed to update ${locationId}: ${error}`);
    }
  }

  return results;
};

/**
 * Get storage location statistics
 */
export const getStorageLocationStatistics = async (): Promise<StorageLocationStatistics> => {
  const response = await getStorageLocations();
  const locations = response.extend.storage_locations;

  const stats: StorageLocationStatistics = {
    total: locations.length,
    available: locations.filter(l => l.is_available).length,
    unavailable: locations.filter(l => !l.is_available).length,
    total_capacity: locations.reduce((sum, l) => sum + l.capacity, 0),
    total_usage: locations.reduce((sum, l) => sum + l.current_usage, 0),
    by_type: {},
  };

  // Calculate statistics by type
  locations.forEach(location => {
    if (!stats.by_type[location.location_type]) {
      stats.by_type[location.location_type] = 0;
    }
    stats.by_type[location.location_type]++;
  });

  return stats;
};

/**
 * Toggle storage location availability
 */
export const toggleStorageLocationAvailability = async (
  locationId: string
): Promise<StorageLocationResponse> => {
  // First get the current location
  const currentResponse = await getStorageLocationById(locationId);
  const currentLocation = currentResponse.extend.storage_location;

  // Toggle the availability
  return updateStorageLocation(locationId, {
    is_available: !currentLocation.is_available,
  });
};

/**
 * Get storage locations by usage level
 */
export const getStorageLocationsByUsage = async (
  usageThreshold: number = 75 // percentage
): Promise<{
  high_usage: StorageLocation[];
  low_usage: StorageLocation[];
  empty: StorageLocation[];
}> => {
  const response = await getStorageLocations();
  const locations = response.extend.storage_locations;

  const result = {
    high_usage: [] as StorageLocation[],
    low_usage: [] as StorageLocation[],
    empty: [] as StorageLocation[],
  };

  locations.forEach(location => {
    const usagePercentage = location.capacity > 0 
      ? (location.current_usage / location.capacity) * 100 
      : 0;

    if (location.current_usage === 0) {
      result.empty.push(location);
    } else if (usagePercentage >= usageThreshold) {
      result.high_usage.push(location);
    } else {
      result.low_usage.push(location);
    }
  });

  return result;
};

/**
 * Find optimal storage location for new item
 */
export const findOptimalStorageLocation = async (
  locationType?: string,
  requiredCapacity: number = 1
): Promise<StorageLocation | null> => {
  const filters: StorageLocationFilters = {
    is_available: true,
    has_capacity: true,
  };

  if (locationType) {
    filters.location_type = locationType;
  }

  const response = await getStorageLocations(filters);
  const availableLocations = response.extend.storage_locations;

  // Filter by required capacity and sort by usage percentage (prefer less used locations)
  const suitableLocations = availableLocations
    .filter(location => (location.capacity - location.current_usage) >= requiredCapacity)
    .sort((a, b) => {
      const aUsagePercent = a.capacity > 0 ? (a.current_usage / a.capacity) : 0;
      const bUsagePercent = b.capacity > 0 ? (b.current_usage / b.capacity) : 0;
      return aUsagePercent - bUsagePercent; // Sort by lowest usage first
    });

  return suitableLocations.length > 0 ? suitableLocations[0] : null;
};
