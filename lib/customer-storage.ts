/**
 * Customer Storage API Service
 * Handles all API calls for customer storage management functionality
 */

import { apiGet, apiPost, apiPatch, apiDelete } from "./api";
import { getAccessToken } from "./auth";
import {
  CustomerStorage,
  CreateCustomerStorageRequest,
  UpdateCustomerStorageRequest,
  RetrieveCustomerStorageRequest,
  AbandonCustomerStorageRequest,
  CustomerStorageResponse,
  CustomerStoragesListResponse,
  SingleCustomerStorageResponse,
  DeleteCustomerStorageResponse,
  CustomerStorageFilters,
  CustomerStorageStatistics,
  BulkRetrieveCustomerStorageRequest,
  BulkAbandonCustomerStorageRequest,
  getStorageStatus,
  getStorageDuration,
  isStorageActive,
  isStorageOverdue,
} from "@/types/customer-storage";

/**
 * Create a new customer storage record
 */
export const createCustomerStorage = async (
  storageData: CreateCustomerStorageRequest
): Promise<CustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiPost<CustomerStorageResponse>(
    "api/customer-storages",
    storageData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
};

/**
 * Get customer storage by ID
 */
export const getCustomerStorageById = async (
  storageId: string
): Promise<SingleCustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiGet<SingleCustomerStorageResponse>(
    `api/customer-storages/${storageId}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
};

/**
 * Get all customer storage records
 */
export const getCustomerStorages = async (
  filters?: CustomerStorageFilters
): Promise<CustomerStoragesListResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // Build query parameters
  const params = new URLSearchParams();

  if (filters) {
    if (filters.customer_id) {
      params.append("customer_id", filters.customer_id);
    }
    if (filters.location_id) {
      params.append("location_id", filters.location_id);
    }
    if (filters.is_retrieved !== undefined) {
      params.append("is_retrieved", filters.is_retrieved.toString());
    }
    if (filters.is_abandoned !== undefined) {
      params.append("is_abandoned", filters.is_abandoned.toString());
    }
    if (filters.storage_date_from) {
      params.append("storage_date_from", filters.storage_date_from);
    }
    if (filters.storage_date_to) {
      params.append("storage_date_to", filters.storage_date_to);
    }
    if (filters.search) {
      params.append("search", filters.search);
    }
    if (filters.page) {
      params.append("page", filters.page.toString());
    }
    if (filters.limit) {
      params.append("limit", filters.limit.toString());
    }
  }

  const queryString = params.toString();
  const url = queryString
    ? `api/customer-storages?${queryString}`
    : "api/customer-storages";

  return apiGet<CustomerStoragesListResponse>(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

/**
 * Update customer storage record
 */
export const updateCustomerStorage = async (
  storageId: string,
  storageData: UpdateCustomerStorageRequest
): Promise<CustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiPatch<CustomerStorageResponse>(
    `api/customer-storages/${storageId}`,
    storageData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
};

/**
 * Delete customer storage record
 */
export const deleteCustomerStorage = async (
  storageId: string
): Promise<DeleteCustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiDelete<DeleteCustomerStorageResponse>(
    `api/customer-storages/${storageId}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
};

/**
 * Retrieve customer storage (mark as retrieved)
 */
export const retrieveCustomerStorage = async (
  storageId: string,
  retrievalData?: RetrieveCustomerStorageRequest
): Promise<CustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiPost<CustomerStorageResponse>(
    `api/customer-storages/${storageId}/retrieve`,
    retrievalData || {},
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
};

/**
 * Abandon customer storage (mark as abandoned)
 */
export const abandonCustomerStorage = async (
  storageId: string,
  abandonmentData?: AbandonCustomerStorageRequest
): Promise<CustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiPost<CustomerStorageResponse>(
    `api/customer-storages/${storageId}/abandon`,
    abandonmentData || {},
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
};

/**
 * Get active customer storage records only
 */
export const getActiveCustomerStorages = async (
  filters?: Omit<CustomerStorageFilters, "is_retrieved" | "is_abandoned">
): Promise<CustomerStoragesListResponse> => {
  return getCustomerStorages({
    ...filters,
    is_retrieved: false,
    is_abandoned: false,
  });
};

/**
 * Get customer storage records by customer
 */
export const getCustomerStoragesByCustomer = async (
  customerId: string,
  filters?: Omit<CustomerStorageFilters, "customer_id">
): Promise<CustomerStoragesListResponse> => {
  return getCustomerStorages({
    ...filters,
    customer_id: customerId,
  });
};

/**
 * Get customer storage records by location
 */
export const getCustomerStoragesByLocation = async (
  locationId: string,
  filters?: Omit<CustomerStorageFilters, "location_id">
): Promise<CustomerStoragesListResponse> => {
  return getCustomerStorages({
    ...filters,
    location_id: locationId,
  });
};

/**
 * Search customer storage records
 */
export const searchCustomerStorages = async (
  searchTerm: string,
  filters?: Omit<CustomerStorageFilters, "search">
): Promise<CustomerStoragesListResponse> => {
  return getCustomerStorages({
    ...filters,
    search: searchTerm,
  });
};

/**
 * Get customer storage records with pagination
 */
export const getCustomerStoragesPaginated = async (
  page: number = 1,
  limit: number = 10,
  filters?: Omit<CustomerStorageFilters, "page" | "limit">
): Promise<CustomerStoragesListResponse> => {
  return getCustomerStorages({
    ...filters,
    page,
    limit,
  });
};

/**
 * Bulk retrieve customer storage records
 */
export const bulkRetrieveCustomerStorages = async (
  bulkData: BulkRetrieveCustomerStorageRequest
): Promise<{ success: number; failed: number; errors: string[] }> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // For now, we'll implement this as individual retrievals
  // In a real implementation, this would be a single API call
  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[],
  };

  for (const storageId of bulkData.storage_ids) {
    try {
      await retrieveCustomerStorage(storageId, {
        retrieval_date: bulkData.retrieval_date,
        notes: bulkData.notes,
      });
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Failed to retrieve ${storageId}: ${error}`);
    }
  }

  return results;
};

/**
 * Bulk abandon customer storage records
 */
export const bulkAbandonCustomerStorages = async (
  bulkData: BulkAbandonCustomerStorageRequest
): Promise<{ success: number; failed: number; errors: string[] }> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // For now, we'll implement this as individual abandonments
  // In a real implementation, this would be a single API call
  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[],
  };

  for (const storageId of bulkData.storage_ids) {
    try {
      await abandonCustomerStorage(storageId, {
        abandonment_date: bulkData.abandonment_date,
        notes: bulkData.notes,
      });
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Failed to abandon ${storageId}: ${error}`);
    }
  }

  return results;
};

/**
 * Get customer storage statistics
 */
export const getCustomerStorageStatistics =
  async (): Promise<CustomerStorageStatistics> => {
    const response = await getCustomerStorages();
    const storages = response.extend.customer_storages;

    const stats: CustomerStorageStatistics = {
      total: storages.length,
      active: 0,
      retrieved: 0,
      abandoned: 0,
      overdue: 0,
      by_location: {},
      by_customer: {},
    };

    storages.forEach((storage) => {
      const status = getStorageStatus(storage);

      // Count by status
      if (status === "stored") {
        stats.active++;
        if (isStorageOverdue(storage)) {
          stats.overdue++;
        }
      } else if (status === "retrieved") {
        stats.retrieved++;
      } else if (status === "abandoned") {
        stats.abandoned++;
      }

      // Count by location
      const locationName = storage.storage_location.location_name;
      if (!stats.by_location[locationName]) {
        stats.by_location[locationName] = 0;
      }
      stats.by_location[locationName]++;

      // Count by customer
      const customerName = `${storage.customer.first_name} ${storage.customer.last_name}`;
      if (!stats.by_customer[customerName]) {
        stats.by_customer[customerName] = 0;
      }
      stats.by_customer[customerName]++;
    });

    return stats;
  };

/**
 * Get overdue customer storage records
 */
export const getOverdueCustomerStorages = async (
  maxDays: number = 30
): Promise<CustomerStorage[]> => {
  const response = await getActiveCustomerStorages();
  const activeStorages = response.extend.customer_storages;

  return activeStorages.filter((storage) => isStorageOverdue(storage, maxDays));
};

/**
 * Get customer storage records by date range
 */
export const getCustomerStoragesByDateRange = async (
  fromDate: string,
  toDate: string,
  filters?: Omit<
    CustomerStorageFilters,
    "storage_date_from" | "storage_date_to"
  >
): Promise<CustomerStoragesListResponse> => {
  return getCustomerStorages({
    ...filters,
    storage_date_from: fromDate,
    storage_date_to: toDate,
  });
};

/**
 * Get customer storage history for a customer
 */
export const getCustomerStorageHistory = async (
  customerId: string
): Promise<CustomerStoragesListResponse> => {
  return getCustomerStoragesByCustomer(customerId);
};

/**
 * Get storage location usage statistics
 */
export const getStorageLocationUsage = async (): Promise<
  Record<
    string,
    {
      total: number;
      active: number;
      retrieved: number;
      abandoned: number;
    }
  >
> => {
  const response = await getCustomerStorages();
  const storages = response.extend.customer_storages;

  const usage: Record<
    string,
    {
      total: number;
      active: number;
      retrieved: number;
      abandoned: number;
    }
  > = {};

  storages.forEach((storage) => {
    const locationName = storage.storage_location.location_name;
    if (!usage[locationName]) {
      usage[locationName] = {
        total: 0,
        active: 0,
        retrieved: 0,
        abandoned: 0,
      };
    }

    usage[locationName].total++;
    const status = getStorageStatus(storage);
    if (status === "stored") {
      usage[locationName].active++;
    } else if (status === "retrieved") {
      usage[locationName].retrieved++;
    } else if (status === "abandoned") {
      usage[locationName].abandoned++;
    }
  });

  return usage;
};
