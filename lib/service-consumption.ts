/**
 * Service Consumption API Service
 * Handles all API calls for service consumption management functionality
 */

import { apiGet, apiPost, apiPatch, apiDelete } from "./api";
import { getAccessToken } from "./auth";
import {
  ServiceConsumption,
  CreateServiceConsumptionRequest,
  UpdateServiceConsumptionRequest,
  UpdatePaymentStatusRequest,
  ServiceConsumptionResponse,
  ServiceConsumptionsListResponse,
  SingleServiceConsumptionResponse,
  DeleteServiceConsumptionResponse,
  ServiceConsumptionFilters,
  ServiceConsumptionStatistics,
  BulkUpdatePaymentStatusRequest,
  BulkDeleteServiceConsumptionRequest,
  getPaymentStatusLabel,
  isPaymentPending,
  isPaymentCompleted,
  calculateTotalAmount,
} from "@/types/service-consumption";

/**
 * Create a new service consumption record
 */
export const createServiceConsumption = async (
  consumptionData: CreateServiceConsumptionRequest
): Promise<ServiceConsumptionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiPost<ServiceConsumptionResponse>(
    "api/service-consumptions/bulk",
    consumptionData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
};

/**
 * Get service consumption by ID
 */
export const getServiceConsumptionById = async (
  consumptionId: string
): Promise<SingleServiceConsumptionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiGet<SingleServiceConsumptionResponse>(
    `api/service-consumptions/${consumptionId}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
};

/**
 * Get all service consumption records
 */
export const getServiceConsumptions = async (
  filters?: ServiceConsumptionFilters
): Promise<ServiceConsumptionsListResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // Build query parameters
  const params = new URLSearchParams();

  if (filters) {
    if (filters.customer_id) {
      params.append("customer_id", filters.customer_id);
    }
    if (filters.service_id) {
      params.append("service_id", filters.service_id);
    }
    if (filters.payment_status) {
      params.append("payment_status", filters.payment_status);
    }
    if (filters.consumption_date_from) {
      params.append("consumption_date_from", filters.consumption_date_from);
    }
    if (filters.consumption_date_to) {
      params.append("consumption_date_to", filters.consumption_date_to);
    }
    if (filters.payment_date_from) {
      params.append("payment_date_from", filters.payment_date_from);
    }
    if (filters.payment_date_to) {
      params.append("payment_date_to", filters.payment_date_to);
    }
    if (filters.search) {
      params.append("search", filters.search);
    }
    if (filters.page) {
      params.append("page", filters.page.toString());
    }
    if (filters.limit) {
      params.append("limit", filters.limit.toString());
    }
  }

  const queryString = params.toString();
  const url = queryString
    ? `api/service-consumptions?${queryString}`
    : "api/service-consumptions";

  return apiGet<ServiceConsumptionsListResponse>(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

/**
 * Update service consumption record
 */
export const updateServiceConsumption = async (
  consumptionId: string,
  consumptionData: UpdateServiceConsumptionRequest
): Promise<ServiceConsumptionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiPatch<ServiceConsumptionResponse>(
    `api/service-consumptions/${consumptionId}`,
    consumptionData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
};

/**
 * Update payment status for service consumption
 */
export const updatePaymentStatus = async (
  consumptionId: string,
  paymentData: UpdatePaymentStatusRequest
): Promise<ServiceConsumptionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiPatch<ServiceConsumptionResponse>(
    `api/service-consumptions/${consumptionId}/payment`,
    paymentData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
};

/**
 * Delete service consumption record
 */
export const deleteServiceConsumption = async (
  consumptionId: string
): Promise<DeleteServiceConsumptionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return apiDelete<DeleteServiceConsumptionResponse>(
    `api/service-consumptions/${consumptionId}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
};

/**
 * Get pending payment service consumptions
 */
export const getPendingPaymentConsumptions = async (
  filters?: Omit<ServiceConsumptionFilters, "payment_status">
): Promise<ServiceConsumptionsListResponse> => {
  return getServiceConsumptions({
    ...filters,
    payment_status: "pending",
  });
};

/**
 * Get paid service consumptions
 */
export const getPaidConsumptions = async (
  filters?: Omit<ServiceConsumptionFilters, "payment_status">
): Promise<ServiceConsumptionsListResponse> => {
  return getServiceConsumptions({
    ...filters,
    payment_status: "paid",
  });
};

/**
 * Get service consumptions by customer
 */
export const getServiceConsumptionsByCustomer = async (
  customerId: string,
  filters?: Omit<ServiceConsumptionFilters, "customer_id">
): Promise<ServiceConsumptionsListResponse> => {
  return getServiceConsumptions({
    ...filters,
    customer_id: customerId,
  });
};

/**
 * Get service consumptions by service
 */
export const getServiceConsumptionsByService = async (
  serviceId: string,
  filters?: Omit<ServiceConsumptionFilters, "service_id">
): Promise<ServiceConsumptionsListResponse> => {
  return getServiceConsumptions({
    ...filters,
    service_id: serviceId,
  });
};

/**
 * Search service consumption records
 */
export const searchServiceConsumptions = async (
  searchTerm: string,
  filters?: Omit<ServiceConsumptionFilters, "search">
): Promise<ServiceConsumptionsListResponse> => {
  return getServiceConsumptions({
    ...filters,
    search: searchTerm,
  });
};

/**
 * Get service consumption records with pagination
 */
export const getServiceConsumptionsPaginated = async (
  page: number = 1,
  limit: number = 10,
  filters?: Omit<ServiceConsumptionFilters, "page" | "limit">
): Promise<ServiceConsumptionsListResponse> => {
  return getServiceConsumptions({
    ...filters,
    page,
    limit,
  });
};

/**
 * Bulk update payment status for multiple service consumptions
 */
export const bulkUpdatePaymentStatus = async (
  bulkData: BulkUpdatePaymentStatusRequest
): Promise<{ success: number; failed: number; errors: string[] }> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // For now, we'll implement this as individual updates
  // In a real implementation, this would be a single API call
  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[],
  };

  for (const consumptionId of bulkData.consumption_ids) {
    try {
      await updatePaymentStatus(consumptionId, {
        payment_status: bulkData.payment_status,
        payment_date: bulkData.payment_date,
        notes: bulkData.notes,
      });
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Failed to update ${consumptionId}: ${error}`);
    }
  }

  return results;
};

/**
 * Bulk delete service consumption records
 */
export const bulkDeleteServiceConsumptions = async (
  bulkData: BulkDeleteServiceConsumptionRequest
): Promise<{ success: number; failed: number; errors: string[] }> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // For now, we'll implement this as individual deletions
  // In a real implementation, this would be a single API call
  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[],
  };

  for (const consumptionId of bulkData.consumption_ids) {
    try {
      await deleteServiceConsumption(consumptionId);
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Failed to delete ${consumptionId}: ${error}`);
    }
  }

  return results;
};

/**
 * Get service consumption total
 */
export const getServiceConsumptionTotal = async (
  filters?: ServiceConsumptionFilters
): Promise<{
  currency: string;
  total_amount: number;
}> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // Build query parameters
  const params = new URLSearchParams();

  if (filters) {
    if (filters.customer_id) {
      params.append("customer_id", filters.customer_id);
    }
    if (filters.service_id) {
      params.append("service_id", filters.service_id);
    }
    if (filters.visit_id) {
      params.append("visit_id", filters.visit_id);
    }
    if (filters.start_date) {
      params.append("start_date", filters.start_date);
    }
    if (filters.end_date) {
      params.append("end_date", filters.end_date);
    }
  }

  const queryString = params.toString();
  const url = queryString
    ? `api/service-consumptions/total?${queryString}`
    : "api/service-consumptions/total";

  const response = await apiGet<{
    extend: {
      currency: string;
      total_amount: number;
    };
    msg: string;
  }>(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.extend;
};

/**
 * Get service consumption statistics
 */
export const getServiceConsumptionStatistics =
  async (): Promise<ServiceConsumptionStatistics> => {
    const response = await getServiceConsumptions();
    const consumptions = response.extend.service_consumptions;

    const stats: ServiceConsumptionStatistics = {
      total: consumptions.length,
      pending_payment: 0,
      paid: 0,
      cancelled: 0,
      refunded: 0,
      total_revenue: 0,
      pending_revenue: 0,
      by_service: {},
      by_customer: {},
      by_month: {},
    };

    consumptions.forEach((consumption) => {
      // Count by payment status
      if (consumption.payment_status === "pending") {
        stats.pending_payment++;
        stats.pending_revenue += consumption.total_amount;
      } else if (consumption.payment_status === "paid") {
        stats.paid++;
        stats.total_revenue += consumption.total_amount;
      } else if (consumption.payment_status === "cancelled") {
        stats.cancelled++;
      } else if (consumption.payment_status === "refunded") {
        stats.refunded++;
      }

      // Count by service
      const serviceName = consumption.service.name;
      if (!stats.by_service[serviceName]) {
        stats.by_service[serviceName] = { count: 0, revenue: 0 };
      }
      stats.by_service[serviceName].count++;
      if (consumption.payment_status === "paid") {
        stats.by_service[serviceName].revenue += consumption.total_amount;
      }

      // Count by customer
      const customerName = `${consumption.customer.first_name} ${consumption.customer.last_name}`;
      if (!stats.by_customer[customerName]) {
        stats.by_customer[customerName] = { count: 0, revenue: 0 };
      }
      stats.by_customer[customerName].count++;
      if (consumption.payment_status === "paid") {
        stats.by_customer[customerName].revenue += consumption.total_amount;
      }

      // Count by month
      const monthKey = new Date(consumption.consumption_date)
        .toISOString()
        .substring(0, 7); // YYYY-MM
      if (!stats.by_month[monthKey]) {
        stats.by_month[monthKey] = { count: 0, revenue: 0 };
      }
      stats.by_month[monthKey].count++;
      if (consumption.payment_status === "paid") {
        stats.by_month[monthKey].revenue += consumption.total_amount;
      }
    });

    return stats;
  };

/**
 * Get service consumption records by date range
 */
export const getServiceConsumptionsByDateRange = async (
  fromDate: string,
  toDate: string,
  filters?: Omit<
    ServiceConsumptionFilters,
    "consumption_date_from" | "consumption_date_to"
  >
): Promise<ServiceConsumptionsListResponse> => {
  return getServiceConsumptions({
    ...filters,
    consumption_date_from: fromDate,
    consumption_date_to: toDate,
  });
};

/**
 * Get customer service consumption history
 */
export const getCustomerConsumptionHistory = async (
  customerId: string
): Promise<ServiceConsumptionsListResponse> => {
  return getServiceConsumptionsByCustomer(customerId);
};

/**
 * Get service usage statistics
 */
export const getServiceUsageStatistics = async (): Promise<
  Record<
    string,
    {
      total_consumptions: number;
      total_revenue: number;
      pending_payments: number;
      pending_revenue: number;
    }
  >
> => {
  const response = await getServiceConsumptions();
  const consumptions = response.extend.service_consumptions;

  const usage: Record<
    string,
    {
      total_consumptions: number;
      total_revenue: number;
      pending_payments: number;
      pending_revenue: number;
    }
  > = {};

  consumptions.forEach((consumption) => {
    const serviceName = consumption.service.name;
    if (!usage[serviceName]) {
      usage[serviceName] = {
        total_consumptions: 0,
        total_revenue: 0,
        pending_payments: 0,
        pending_revenue: 0,
      };
    }

    usage[serviceName].total_consumptions++;

    if (consumption.payment_status === "paid") {
      usage[serviceName].total_revenue += consumption.total_amount;
    } else if (consumption.payment_status === "pending") {
      usage[serviceName].pending_payments++;
      usage[serviceName].pending_revenue += consumption.total_amount;
    }
  });

  return usage;
};

/**
 * Mark multiple consumptions as paid
 */
export const markConsumptionsAsPaid = async (
  consumptionIds: string[],
  paymentDate?: string
): Promise<{ success: number; failed: number; errors: string[] }> => {
  return bulkUpdatePaymentStatus({
    consumption_ids: consumptionIds,
    payment_status: "paid",
    payment_date: paymentDate || new Date().toISOString().split("T")[0],
  });
};

/**
 * Cancel multiple consumptions
 */
export const cancelConsumptions = async (
  consumptionIds: string[]
): Promise<{ success: number; failed: number; errors: string[] }> => {
  return bulkUpdatePaymentStatus({
    consumption_ids: consumptionIds,
    payment_status: "cancelled",
  });
};
