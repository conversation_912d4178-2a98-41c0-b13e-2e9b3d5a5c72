/**
 * Storage Location Management Types
 * Types for storage location functionality (lockers, storage areas, etc.)
 */

// Base StorageLocation interface
export interface StorageLocation {
  location_id: string;
  location_name: string;
  location_type: string;
  capacity: number;
  current_usage: number;
  is_available: boolean;
  description?: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
}

// Storage location creation request
export interface CreateStorageLocationRequest {
  location_name: string;
  location_type: string;
  capacity: number;
  description?: string;
}

// Storage location update request
export interface UpdateStorageLocationRequest {
  location_name?: string;
  location_type?: string;
  capacity?: number;
  description?: string;
  is_available?: boolean;
}

// API Response types
export interface StorageLocationResponse {
  extend: {
    message: string;
    storage_location: StorageLocation;
  };
  msg: string;
}

export interface StorageLocationsListResponse {
  extend: {
    count: number;
    storage_locations: StorageLocation[];
  };
  msg: string;
}

export interface SingleStorageLocationResponse {
  extend: {
    storage_location: StorageLocation;
  };
  msg: string;
}

export interface DeleteStorageLocationResponse {
  extend: {
    message: string;
  };
  msg: string;
}

// Storage location filters for listing
export interface StorageLocationFilters {
  location_type?: string;
  is_available?: boolean;
  has_capacity?: boolean; // Filter for locations with available capacity
  search?: string;
  page?: number;
  limit?: number;
}

// Form data interface
export interface StorageLocationFormData {
  location_name: string;
  location_type: string;
  capacity: string; // String for form input
  description: string;
}

// Storage location types
export const STORAGE_LOCATION_TYPES = [
  { value: 'locker', label: 'Locker' },
  { value: 'cabinet', label: 'Cabinet' },
  { value: 'shelf', label: 'Shelf' },
  { value: 'room', label: 'Room' },
  { value: 'area', label: 'Area' },
  { value: 'bin', label: 'Bin' },
  { value: 'drawer', label: 'Drawer' },
  { value: 'compartment', label: 'Compartment' },
] as const;

export type StorageLocationType = typeof STORAGE_LOCATION_TYPES[number]['value'];

// Utility functions
export const formatLocationName = (location: StorageLocation): string => {
  return location.location_name;
};

export const getLocationTypeLabel = (type: string): string => {
  const typeOption = STORAGE_LOCATION_TYPES.find(t => t.value === type);
  return typeOption ? typeOption.label : type;
};

export const getAvailabilityLabel = (isAvailable: boolean): string => {
  return isAvailable ? 'Available' : 'Unavailable';
};

export const getAvailabilityColor = (isAvailable: boolean): string => {
  return isAvailable 
    ? 'bg-green-100 text-green-800' 
    : 'bg-red-100 text-red-800';
};

export const getCapacityUsagePercentage = (location: StorageLocation): number => {
  if (location.capacity === 0) return 0;
  return Math.round((location.current_usage / location.capacity) * 100);
};

export const getCapacityStatusColor = (location: StorageLocation): string => {
  const percentage = getCapacityUsagePercentage(location);
  if (percentage >= 90) return 'bg-red-500';
  if (percentage >= 75) return 'bg-yellow-500';
  if (percentage >= 50) return 'bg-blue-500';
  return 'bg-green-500';
};

export const getCapacityStatusLabel = (location: StorageLocation): string => {
  const percentage = getCapacityUsagePercentage(location);
  if (percentage >= 90) return 'Nearly Full';
  if (percentage >= 75) return 'High Usage';
  if (percentage >= 50) return 'Moderate Usage';
  if (percentage > 0) return 'Low Usage';
  return 'Empty';
};

export const hasAvailableCapacity = (location: StorageLocation): boolean => {
  return location.current_usage < location.capacity;
};

export const getAvailableCapacity = (location: StorageLocation): number => {
  return Math.max(0, location.capacity - location.current_usage);
};

export const isLocationActive = (location: StorageLocation): boolean => {
  return location.is_available;
};

export const validateLocationName = (name: string): string | null => {
  if (!name || name.trim().length === 0) {
    return 'Location name is required';
  }
  
  if (name.trim().length < 2) {
    return 'Location name must be at least 2 characters long';
  }
  
  if (name.trim().length > 100) {
    return 'Location name must be less than 100 characters';
  }
  
  return null;
};

export const validateCapacity = (capacity: string): string | null => {
  if (!capacity || capacity.trim().length === 0) {
    return 'Capacity is required';
  }
  
  const numericCapacity = parseInt(capacity);
  if (isNaN(numericCapacity)) {
    return 'Capacity must be a valid number';
  }
  
  if (numericCapacity <= 0) {
    return 'Capacity must be greater than 0';
  }
  
  if (numericCapacity > 10000) {
    return 'Capacity cannot exceed 10,000';
  }
  
  return null;
};

export const validateDescription = (description: string): string | null => {
  if (description && description.length > 500) {
    return 'Description must be less than 500 characters';
  }
  
  return null;
};

export const validateStorageLocationForm = (formData: StorageLocationFormData): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  const nameError = validateLocationName(formData.location_name);
  if (nameError) {
    errors.location_name = nameError;
  }
  
  if (!formData.location_type) {
    errors.location_type = 'Location type is required';
  }
  
  const capacityError = validateCapacity(formData.capacity);
  if (capacityError) {
    errors.capacity = capacityError;
  }
  
  const descriptionError = validateDescription(formData.description);
  if (descriptionError) {
    errors.description = descriptionError;
  }
  
  return errors;
};

// Default storage location form data
export const getDefaultStorageLocationFormData = (): StorageLocationFormData => ({
  location_name: '',
  location_type: '',
  capacity: '',
  description: '',
});

// Storage location sorting options
export const STORAGE_LOCATION_SORT_OPTIONS = [
  { value: 'name_asc', label: 'Name (A-Z)' },
  { value: 'name_desc', label: 'Name (Z-A)' },
  { value: 'type_asc', label: 'Type (A-Z)' },
  { value: 'type_desc', label: 'Type (Z-A)' },
  { value: 'capacity_asc', label: 'Capacity (Low to High)' },
  { value: 'capacity_desc', label: 'Capacity (High to Low)' },
  { value: 'usage_asc', label: 'Usage (Low to High)' },
  { value: 'usage_desc', label: 'Usage (High to Low)' },
  { value: 'available_first', label: 'Available First' },
  { value: 'created_desc', label: 'Newest First' },
  { value: 'created_asc', label: 'Oldest First' },
] as const;

export type StorageLocationSortOption = typeof STORAGE_LOCATION_SORT_OPTIONS[number]['value'];

// Storage location statistics
export interface StorageLocationStatistics {
  total: number;
  available: number;
  unavailable: number;
  total_capacity: number;
  total_usage: number;
  by_type: Record<string, number>;
}

// Bulk operations
export interface BulkUpdateStorageLocationRequest {
  location_ids: string[];
  updates: Partial<UpdateStorageLocationRequest>;
}

// Location search and filtering
export const searchStorageLocations = (
  locations: StorageLocation[],
  searchTerm: string
): StorageLocation[] => {
  if (!searchTerm.trim()) return locations;
  
  const term = searchTerm.toLowerCase();
  return locations.filter(location =>
    location.location_name.toLowerCase().includes(term) ||
    location.location_type.toLowerCase().includes(term) ||
    (location.description && location.description.toLowerCase().includes(term))
  );
};

export const filterStorageLocationsByType = (
  locations: StorageLocation[],
  type: string
): StorageLocation[] => {
  if (!type) return locations;
  return locations.filter(location => location.location_type === type);
};

export const filterStorageLocationsByAvailability = (
  locations: StorageLocation[],
  isAvailable: boolean
): StorageLocation[] => {
  return locations.filter(location => location.is_available === isAvailable);
};

export const filterStorageLocationsByCapacity = (
  locations: StorageLocation[],
  hasCapacity: boolean
): StorageLocation[] => {
  return locations.filter(location => 
    hasCapacity ? hasAvailableCapacity(location) : !hasAvailableCapacity(location)
  );
};
