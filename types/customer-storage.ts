/**
 * Customer Storage Management Types
 * Types for managing customer belongings storage, retrieval, and abandonment
 */

import { Customer } from "./customer";
import { StorageLocation } from "./storage-location";

// Base CustomerStorage interface
export interface CustomerStorage {
  storage_id: string;
  customer_id: string;
  location_id: string;
  item_description: string;
  storage_date: string; // ISO date string
  retrieval_date?: string | null; // ISO date string
  is_abandoned: boolean;
  abandonment_date?: string | null; // ISO date string
  notes?: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  customer: Customer;
  storage_location: StorageLocation;
}

// Customer storage creation request
export interface CreateCustomerStorageRequest {
  customer_id: string;
  location_id: string;
  item_description: string;
  storage_date?: string; // ISO date string, defaults to today
  notes?: string;
}

// Customer storage update request
export interface UpdateCustomerStorageRequest {
  location_id?: string;
  item_description?: string;
  storage_date?: string;
  notes?: string;
}

// Customer storage retrieval request
export interface RetrieveCustomerStorageRequest {
  retrieval_date?: string; // ISO date string, defaults to today
  notes?: string;
}

// Customer storage abandonment request
export interface AbandonCustomerStorageRequest {
  abandonment_date?: string; // ISO date string, defaults to today
  notes?: string;
}

// API Response types
export interface CustomerStorageResponse {
  extend: {
    message: string;
    customer_storage: CustomerStorage;
  };
  msg: string;
}

export interface CustomerStoragesListResponse {
  extend: {
    count: number;
    customer_storages: CustomerStorage[];
  };
  msg: string;
}

export interface SingleCustomerStorageResponse {
  extend: {
    customer_storage: CustomerStorage;
  };
  msg: string;
}

export interface DeleteCustomerStorageResponse {
  extend: {
    message: string;
  };
  msg: string;
}

// Customer storage filters for listing
export interface CustomerStorageFilters {
  customer_id?: string;
  location_id?: string;
  is_retrieved?: boolean; // Filter for retrieved/not retrieved items
  is_abandoned?: boolean; // Filter for abandoned items
  storage_date_from?: string; // ISO date string
  storage_date_to?: string; // ISO date string
  search?: string;
  page?: number;
  limit?: number;
}

// Form data interface
export interface CustomerStorageFormData {
  customer_id: string;
  location_id: string;
  item_description: string;
  storage_date: string;
  notes: string;
}

// Storage status types
export type StorageStatus = "stored" | "retrieved" | "abandoned";

// Utility functions
export const getStorageStatus = (
  customerStorage: CustomerStorage
): StorageStatus => {
  if (customerStorage.is_abandoned) return "abandoned";
  if (customerStorage.retrieval_date) return "retrieved";
  return "stored";
};

export const getStorageStatusLabel = (status: StorageStatus): string => {
  switch (status) {
    case "stored":
      return "Stored";
    case "retrieved":
      return "Retrieved";
    case "abandoned":
      return "Abandoned";
    default:
      return "Unknown";
  }
};

export const getStorageStatusColor = (status: StorageStatus): string => {
  switch (status) {
    case "stored":
      return "bg-blue-100 text-blue-800";
    case "retrieved":
      return "bg-green-100 text-green-800";
    case "abandoned":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const isStorageActive = (customerStorage: CustomerStorage): boolean => {
  return !customerStorage.retrieval_date && !customerStorage.is_abandoned;
};

export const getStorageDuration = (
  customerStorage: CustomerStorage
): number => {
  const startDate = new Date(customerStorage.storage_date);
  const endDate = customerStorage.retrieval_date
    ? new Date(customerStorage.retrieval_date)
    : customerStorage.abandonment_date
    ? new Date(customerStorage.abandonment_date)
    : new Date();

  return Math.ceil(
    (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
  );
};

export const formatStorageDuration = (days: number): string => {
  if (days === 1) return "1 day";
  if (days < 30) return `${days} days`;
  if (days < 365) {
    const months = Math.floor(days / 30);
    return months === 1 ? "1 month" : `${months} months`;
  }
  const years = Math.floor(days / 365);
  return years === 1 ? "1 year" : `${years} years`;
};

export const isStorageOverdue = (
  customerStorage: CustomerStorage,
  maxDays: number = 30
): boolean => {
  if (!isStorageActive(customerStorage)) return false;
  return getStorageDuration(customerStorage) > maxDays;
};

export const validateItemDescription = (description: string): string | null => {
  if (!description || description.trim().length === 0) {
    return "Item description is required";
  }

  if (description.trim().length < 3) {
    return "Item description must be at least 3 characters long";
  }

  if (description.trim().length > 500) {
    return "Item description must be less than 500 characters";
  }

  return null;
};

export const validateStorageDate = (date: string): string | null => {
  if (!date || date.trim().length === 0) {
    return "Storage date is required";
  }

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    return "Storage date must be a valid date";
  }

  const today = new Date();
  today.setHours(23, 59, 59, 999); // End of today
  if (dateObj > today) {
    return "Storage date cannot be in the future";
  }

  return null;
};

export const validateNotes = (notes: string): string | null => {
  if (notes && notes.length > 1000) {
    return "Notes must be less than 1000 characters";
  }

  return null;
};

export const validateCustomerStorageForm = (
  formData: CustomerStorageFormData
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!formData.customer_id) {
    errors.customer_id = "Customer is required";
  }

  if (!formData.location_id) {
    errors.location_id = "Storage location is required";
  }

  const descriptionError = validateItemDescription(formData.item_description);
  if (descriptionError) {
    errors.item_description = descriptionError;
  }

  const dateError = validateStorageDate(formData.storage_date);
  if (dateError) {
    errors.storage_date = dateError;
  }

  const notesError = validateNotes(formData.notes);
  if (notesError) {
    errors.notes = notesError;
  }

  return errors;
};

// Default customer storage form data
export const getDefaultCustomerStorageFormData =
  (): CustomerStorageFormData => ({
    customer_id: "",
    location_id: "",
    item_description: "",
    storage_date: new Date().toISOString().split("T")[0], // Today's date
    notes: "",
  });

// Customer storage sorting options
export const CUSTOMER_STORAGE_SORT_OPTIONS = [
  { value: "customer_name_asc", label: "Customer Name (A-Z)" },
  { value: "customer_name_desc", label: "Customer Name (Z-A)" },
  { value: "storage_date_desc", label: "Storage Date (Newest First)" },
  { value: "storage_date_asc", label: "Storage Date (Oldest First)" },
  { value: "retrieval_date_desc", label: "Retrieval Date (Newest First)" },
  { value: "location_name_asc", label: "Location Name (A-Z)" },
  { value: "status_active", label: "Active Storage First" },
  { value: "duration_desc", label: "Longest Duration First" },
] as const;

export type CustomerStorageSortOption =
  (typeof CUSTOMER_STORAGE_SORT_OPTIONS)[number]["value"];

// Customer storage statistics
export interface CustomerStorageStatistics {
  total: number;
  active: number;
  retrieved: number;
  abandoned: number;
  overdue: number;
  by_location: Record<string, number>;
  by_customer: Record<string, number>;
}

// Bulk operations
export interface BulkRetrieveCustomerStorageRequest {
  storage_ids: string[];
  retrieval_date?: string;
  notes?: string;
}

export interface BulkAbandonCustomerStorageRequest {
  storage_ids: string[];
  abandonment_date?: string;
  notes?: string;
}

// Search and filtering
export const searchCustomerStorages = (
  storages: CustomerStorage[],
  searchTerm: string
): CustomerStorage[] => {
  if (!searchTerm.trim()) return storages;

  const term = searchTerm.toLowerCase();
  return storages.filter(
    (storage) =>
      storage.customer.first_name.toLowerCase().includes(term) ||
      storage.customer.last_name.toLowerCase().includes(term) ||
      (storage.customer.email &&
        storage.customer.email.toLowerCase().includes(term)) ||
      storage.item_description.toLowerCase().includes(term) ||
      storage.storage_location.location_name.toLowerCase().includes(term) ||
      (storage.notes && storage.notes.toLowerCase().includes(term))
  );
};

export const filterCustomerStoragesByStatus = (
  storages: CustomerStorage[],
  status: StorageStatus
): CustomerStorage[] => {
  return storages.filter((storage) => getStorageStatus(storage) === status);
};

export const filterCustomerStoragesByCustomer = (
  storages: CustomerStorage[],
  customerId: string
): CustomerStorage[] => {
  return storages.filter((storage) => storage.customer_id === customerId);
};

export const filterCustomerStoragesByLocation = (
  storages: CustomerStorage[],
  locationId: string
): CustomerStorage[] => {
  return storages.filter((storage) => storage.location_id === locationId);
};

export const filterCustomerStoragesByDateRange = (
  storages: CustomerStorage[],
  fromDate: string,
  toDate: string
): CustomerStorage[] => {
  const from = new Date(fromDate);
  const to = new Date(toDate);

  return storages.filter((storage) => {
    const storageDate = new Date(storage.storage_date);
    return storageDate >= from && storageDate <= to;
  });
};
