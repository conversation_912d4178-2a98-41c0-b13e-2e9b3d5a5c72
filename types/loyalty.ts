/**
 * Customer Loyalty Types and Interfaces
 * Based on API specifications for loyalty management system
 */

// ============================================================================
// CORE LOYALTY INTERFACES
// ============================================================================

/**
 * Customer loyalty balance item
 */
export interface LoyaltyBalance {
  balance_id: string;
  customer_id: string;
  rule_id: string;
  reward_type: string;
  balance: number;
  earned_date: string;
  expiry_date: string | null;
  status: 'active' | 'expired' | 'redeemed';
  created_at: string;
  updated_at: string;
}

/**
 * Customer reward redemption record
 */
export interface RewardRedemption {
  redemption_id: string;
  customer_id: string;
  rule_id: string;
  reward_type: string;
  reward_value: number;
  redeemed_at: string;
  visit_id?: string;
  notes?: string;
  status: 'completed' | 'pending' | 'cancelled';
  created_at: string;
  updated_at: string;
}

/**
 * Customer loyalty summary
 */
export interface CustomerLoyalty {
  customer_id: string;
  customer_name: string;
  customer_segment: string;
  total_rewards_earned: number;
  total_rewards_redeemed: number;
  total_rewards_available: number;
  active_balances: LoyaltyBalance[];
  recent_redemptions: RewardRedemption[];
}

/**
 * Customer rewards information
 */
export interface CustomerRewards {
  eligible: boolean;
  total_available_rewards: number;
  balances: LoyaltyBalance[];
}

// ============================================================================
// API RESPONSE INTERFACES
// ============================================================================

/**
 * Customer loyalty summary API response
 */
export interface CustomerLoyaltyResponse {
  extend: {
    loyalty: CustomerLoyalty;
  };
  msg: string;
}

/**
 * Customer rewards API response
 */
export interface CustomerRewardsResponse {
  extend: {
    rewards: CustomerRewards;
  };
  msg: string;
}

/**
 * Loyalty balances list API response
 */
export interface LoyaltyBalancesResponse {
  extend: {
    count: number;
    balances: LoyaltyBalance[];
  };
  msg: string;
}

/**
 * Customer redemptions list API response
 */
export interface CustomerRedemptionsResponse {
  extend: {
    count: number;
    redemptions: RewardRedemption[];
  };
  msg: string;
}

/**
 * Reward redemption request
 */
export interface RedeemRewardRequest {
  rule_id: string;
}

/**
 * Reward redemption API response
 */
export interface RedeemRewardResponse {
  extend: {
    redemption: RewardRedemption;
  };
  msg: string;
}

// ============================================================================
// LOYALTY ANALYTICS INTERFACES
// ============================================================================

/**
 * Loyalty analytics period
 */
export interface LoyaltyAnalyticsPeriod {
  start_date: string;
  end_date: string;
}

/**
 * Redemptions by reward type breakdown
 */
export interface RedemptionsByRewardType {
  [rewardType: string]: number;
}

/**
 * Loyalty analytics data
 */
export interface LoyaltyAnalytics {
  period: LoyaltyAnalyticsPeriod;
  customers_with_rewards: number;
  total_rewards_earned: number;
  total_rewards_redeemed: number;
  total_rewards_available: number;
  redemptions: {
    total: number;
    by_reward_type: RedemptionsByRewardType;
  };
}

/**
 * Loyalty analytics API response
 */
export interface LoyaltyAnalyticsResponse {
  extend: {
    analytics: LoyaltyAnalytics;
  };
  msg: string;
}

// ============================================================================
// FILTER AND QUERY INTERFACES
// ============================================================================

/**
 * Loyalty analytics filters
 */
export interface LoyaltyAnalyticsFilters {
  start_date?: string;
  end_date?: string;
}

/**
 * Customer redemptions filters
 */
export interface CustomerRedemptionsFilters {
  customer_id?: string;
  start_date?: string;
  end_date?: string;
  reward_type?: string;
  status?: 'completed' | 'pending' | 'cancelled';
  page?: number;
  per_page?: number;
}

/**
 * Loyalty balances filters
 */
export interface LoyaltyBalancesFilters {
  customer_id?: string;
  reward_type?: string;
  status?: 'active' | 'expired' | 'redeemed';
  page?: number;
  per_page?: number;
}

// ============================================================================
// UTILITY INTERFACES AND CONSTANTS
// ============================================================================

/**
 * Loyalty status options
 */
export const LOYALTY_STATUS = {
  ACTIVE: 'active',
  EXPIRED: 'expired',
  REDEEMED: 'redeemed',
} as const;

/**
 * Redemption status options
 */
export const REDEMPTION_STATUS = {
  COMPLETED: 'completed',
  PENDING: 'pending',
  CANCELLED: 'cancelled',
} as const;

/**
 * Reward type display names
 */
export const REWARD_TYPE_LABELS = {
  free_visit: 'Free Visit',
  discount_percentage: 'Percentage Discount',
  discount_amount: 'Amount Discount',
  loyalty_points: 'Loyalty Points',
  free_service: 'Free Service',
} as const;

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Format reward value for display
 */
export const formatRewardValue = (rewardType: string, value: number): string => {
  switch (rewardType) {
    case 'discount_percentage':
      return `${value}%`;
    case 'discount_amount':
      return `$${value.toFixed(2)}`;
    case 'loyalty_points':
      return `${value} points`;
    case 'free_visit':
      return `${value} visit${value !== 1 ? 's' : ''}`;
    case 'free_service':
      return `${value} service${value !== 1 ? 's' : ''}`;
    default:
      return value.toString();
  }
};

/**
 * Get status color for loyalty balance
 */
export const getLoyaltyStatusColor = (status: string): string => {
  switch (status) {
    case 'active':
      return 'text-green-600 bg-green-100';
    case 'expired':
      return 'text-red-600 bg-red-100';
    case 'redeemed':
      return 'text-gray-600 bg-gray-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

/**
 * Get status color for redemption
 */
export const getRedemptionStatusColor = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'text-green-600 bg-green-100';
    case 'pending':
      return 'text-yellow-600 bg-yellow-100';
    case 'cancelled':
      return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

/**
 * Check if loyalty balance is expiring soon (within 7 days)
 */
export const isBalanceExpiringSoon = (expiryDate: string | null): boolean => {
  if (!expiryDate) return false;
  
  const expiry = new Date(expiryDate);
  const now = new Date();
  const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  
  return expiry <= sevenDaysFromNow && expiry > now;
};

/**
 * Calculate total available rewards by type
 */
export const calculateRewardsByType = (balances: LoyaltyBalance[]): RedemptionsByRewardType => {
  return balances
    .filter(balance => balance.status === 'active')
    .reduce((acc, balance) => {
      acc[balance.reward_type] = (acc[balance.reward_type] || 0) + balance.balance;
      return acc;
    }, {} as RedemptionsByRewardType);
};
